<?php
/**
 * Admin functionality for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu page
        add_menu_page(
            __('AI Fitness Blog Writer', 'ai-fitness-blog-writer'),
            __('AI Blog Writer', 'ai-fitness-blog-writer'),
            'manage_options',
            'ai-fitness-blog-writer',
            array($this, 'admin_page'),
            'dashicons-edit-large',
            30
        );
        
        // Generator submenu
        add_submenu_page(
            'ai-fitness-blog-writer',
            __('Generate Blog Post', 'ai-fitness-blog-writer'),
            __('Generate Post', 'ai-fitness-blog-writer'),
            'manage_options',
            'ai-fitness-blog-writer',
            array($this, 'admin_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'ai-fitness-blog-writer',
            __('Settings', 'ai-fitness-blog-writer'),
            __('Settings', 'ai-fitness-blog-writer'),
            'manage_options',
            'afbw-settings',
            array($this, 'settings_page')
        );
        
        // Generated Posts submenu
        add_submenu_page(
            'ai-fitness-blog-writer',
            __('Generated Posts', 'ai-fitness-blog-writer'),
            __('Generated Posts', 'ai-fitness-blog-writer'),
            'manage_options',
            'afbw-generated-posts',
            array($this, 'generated_posts_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'ai-fitness-blog-writer') === false && strpos($hook, 'afbw-') === false) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'afbw-admin-style',
            AFBW_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            AFBW_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'afbw-admin-script',
            AFBW_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            AFBW_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('afbw-admin-script', 'afbw_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('afbw_nonce'),
            'strings' => array(
                'generating' => __('Generating content...', 'ai-fitness-blog-writer'),
                'error' => __('An error occurred. Please try again.', 'ai-fitness-blog-writer'),
                'success' => __('Content generated successfully!', 'ai-fitness-blog-writer'),
                'confirm_publish' => __('Are you sure you want to publish this post?', 'ai-fitness-blog-writer')
            )
        ));
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        // Add admin notices
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    /**
     * Display admin notices
     */
    public function admin_notices() {
        $settings = get_option('afbw_settings', array());
        
        // Check if API keys are configured
        if (empty($settings['openrouter_api_key']) || empty($settings['pexels_api_key'])) {
            $settings_url = admin_url('admin.php?page=afbw-settings');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>' . sprintf(
                __('AI Fitness Blog Writer: Please configure your API keys in the <a href="%s">settings page</a> to start generating content.', 'ai-fitness-blog-writer'),
                $settings_url
            ) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap afbw-admin-wrap">
            <h1><?php _e('AI Fitness Blog Writer', 'ai-fitness-blog-writer'); ?></h1>
            <p class="description"><?php _e('Generate engaging fitness blog posts using AI and automatically set featured images.', 'ai-fitness-blog-writer'); ?></p>
            
            <div class="afbw-generator-container">
                <form id="afbw-generator-form" class="afbw-form">
                    <?php wp_nonce_field('afbw_generate_post', 'afbw_nonce'); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="blog_topic"><?php _e('Blog Topic', 'ai-fitness-blog-writer'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="blog_topic" name="blog_topic" class="regular-text" 
                                       placeholder="<?php _e('e.g., Intermittent fasting for weight loss', 'ai-fitness-blog-writer'); ?>" required>
                                <p class="description"><?php _e('Enter the main topic for your blog post.', 'ai-fitness-blog-writer'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="target_audience"><?php _e('Target Audience', 'ai-fitness-blog-writer'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="target_audience" name="target_audience" class="regular-text" 
                                       placeholder="<?php _e('e.g., Beginners, Advanced athletes, Seniors', 'ai-fitness-blog-writer'); ?>">
                                <p class="description"><?php _e('Specify who this content is for.', 'ai-fitness-blog-writer'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="keywords"><?php _e('Keywords', 'ai-fitness-blog-writer'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="keywords" name="keywords" class="regular-text" 
                                       placeholder="<?php _e('weight loss, nutrition, exercise, health', 'ai-fitness-blog-writer'); ?>">
                                <p class="description"><?php _e('Comma-separated keywords to include in the content.', 'ai-fitness-blog-writer'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="word_count"><?php _e('Word Count', 'ai-fitness-blog-writer'); ?></label>
                            </th>
                            <td>
                                <select id="word_count" name="word_count">
                                    <option value="300">300 words</option>
                                    <option value="500">500 words</option>
                                    <option value="800" selected>800 words</option>
                                    <option value="1000">1000 words</option>
                                    <option value="1500">1500 words</option>
                                    <option value="2000">2000 words</option>
                                </select>
                                <p class="description"><?php _e('Select the desired length for your blog post.', 'ai-fitness-blog-writer'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary" id="generate-article-btn">
                            <?php _e('Generate Article', 'ai-fitness-blog-writer'); ?>
                        </button>
                        <span class="spinner" id="afbw-spinner"></span>
                    </p>
                </form>
                
                <div id="afbw-preview-container" class="afbw-preview-container" style="display: none;">
                    <h2><?php _e('Generated Content Preview', 'ai-fitness-blog-writer'); ?></h2>
                    <div id="afbw-preview-content" class="afbw-preview-content"></div>
                    
                    <div class="afbw-preview-actions">
                        <button type="button" class="button button-secondary" id="edit-content-btn">
                            <?php _e('Edit Content', 'ai-fitness-blog-writer'); ?>
                        </button>
                        <button type="button" class="button button-primary" id="publish-post-btn">
                            <?php _e('Publish Post', 'ai-fitness-blog-writer'); ?>
                        </button>
                        <button type="button" class="button" id="save-draft-btn">
                            <?php _e('Save as Draft', 'ai-fitness-blog-writer'); ?>
                        </button>
                    </div>
                </div>
                
                <div id="afbw-edit-container" class="afbw-edit-container" style="display: none;">
                    <h2><?php _e('Edit Generated Content', 'ai-fitness-blog-writer'); ?></h2>
                    <form id="afbw-edit-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="post_title"><?php _e('Post Title', 'ai-fitness-blog-writer'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="post_title" name="post_title" class="large-text" required>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="post_content"><?php _e('Post Content', 'ai-fitness-blog-writer'); ?></label>
                                </th>
                                <td>
                                    <?php
                                    wp_editor('', 'post_content', array(
                                        'textarea_name' => 'post_content',
                                        'textarea_rows' => 20,
                                        'media_buttons' => true,
                                        'teeny' => false,
                                        'tinymce' => true
                                    ));
                                    ?>
                                </td>
                            </tr>
                        </table>
                        
                        <p class="submit">
                            <button type="button" class="button button-secondary" id="back-to-preview-btn">
                                <?php _e('Back to Preview', 'ai-fitness-blog-writer'); ?>
                            </button>
                            <button type="submit" class="button button-primary" id="save-edited-post-btn">
                                <?php _e('Save Changes & Publish', 'ai-fitness-blog-writer'); ?>
                            </button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        require_once AFBW_PLUGIN_PATH . 'includes/admin-settings.php';
    }
    
    /**
     * Generated posts page
     */
    public function generated_posts_page() {
        require_once AFBW_PLUGIN_PATH . 'includes/admin-generated-posts.php';
    }
}
