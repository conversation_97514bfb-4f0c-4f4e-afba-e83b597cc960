<?php
/**
 * Settings page template for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit'])) {
    check_admin_referer('afbw_settings_nonce');
    
    // Update settings
    $settings = array();
    $settings['openrouter_api_key'] = sanitize_text_field($_POST['afbw_settings']['openrouter_api_key']);
    $settings['openrouter_model'] = sanitize_text_field($_POST['afbw_settings']['openrouter_model']);
    $settings['pexels_api_key'] = sanitize_text_field($_POST['afbw_settings']['pexels_api_key']);
    $settings['default_word_count'] = absint($_POST['afbw_settings']['default_word_count']);
    $settings['auto_publish'] = isset($_POST['afbw_settings']['auto_publish']) ? true : false;
    $settings['auto_set_featured_image'] = isset($_POST['afbw_settings']['auto_set_featured_image']) ? true : false;
    $settings['post_status'] = sanitize_text_field($_POST['afbw_settings']['post_status']);
    $settings['post_category'] = absint($_POST['afbw_settings']['post_category']);
    
    update_option('afbw_settings', $settings);
    
    echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'ai-fitness-blog-writer') . '</p></div>';
}

$settings = get_option('afbw_settings', array());
?>

<div class="wrap afbw-settings-wrap">
    <h1><?php _e('AI Fitness Blog Writer Settings', 'ai-fitness-blog-writer'); ?></h1>
    
    <div class="afbw-settings-container">
        <form method="post" action="">
            <?php wp_nonce_field('afbw_settings_nonce'); ?>
            
            <!-- API Configuration Section -->
            <div class="afbw-settings-section">
                <h2><?php _e('API Configuration', 'ai-fitness-blog-writer'); ?></h2>
                <p class="description"><?php _e('Configure your API keys for OpenRouter.ai and Pexels to enable content generation and image fetching.', 'ai-fitness-blog-writer'); ?></p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="openrouter_api_key"><?php _e('OpenRouter API Key', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="openrouter_api_key" name="afbw_settings[openrouter_api_key]" 
                                   value="<?php echo esc_attr($settings['openrouter_api_key'] ?? ''); ?>" class="regular-text" />
                            <button type="button" class="button button-secondary" id="test-openrouter-btn" style="margin-left: 10px;">
                                <?php _e('Test Connection', 'ai-fitness-blog-writer'); ?>
                            </button>
                            <p class="description">
                                <?php printf(
                                    __('Get your API key from <a href="%s" target="_blank">OpenRouter.ai</a>. DeepSeek models are free to use.', 'ai-fitness-blog-writer'),
                                    'https://openrouter.ai/keys'
                                ); ?>
                            </p>
                            <div id="openrouter-test-result" class="afbw-test-result"></div>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="openrouter_model"><?php _e('OpenRouter Model', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $models = array(
                                'deepseek/deepseek-chat' => 'DeepSeek Chat (Free)',
                                'deepseek/deepseek-r1' => 'DeepSeek R1 (Free)',
                                'openai/gpt-3.5-turbo' => 'GPT-3.5 Turbo',
                                'openai/gpt-4' => 'GPT-4',
                                'anthropic/claude-3-haiku' => 'Claude 3 Haiku',
                                'meta-llama/llama-3.1-8b-instruct' => 'Llama 3.1 8B'
                            );
                            $selected_model = $settings['openrouter_model'] ?? 'deepseek/deepseek-chat';
                            ?>
                            <select id="openrouter_model" name="afbw_settings[openrouter_model]" class="regular-text">
                                <?php foreach ($models as $model_id => $model_name): ?>
                                    <option value="<?php echo esc_attr($model_id); ?>" <?php selected($selected_model, $model_id); ?>>
                                        <?php echo esc_html($model_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Select the AI model to use for content generation. DeepSeek models are free.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="pexels_api_key"><?php _e('Pexels API Key', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="pexels_api_key" name="afbw_settings[pexels_api_key]" 
                                   value="<?php echo esc_attr($settings['pexels_api_key'] ?? ''); ?>" class="regular-text" />
                            <button type="button" class="button button-secondary" id="test-pexels-btn" style="margin-left: 10px;">
                                <?php _e('Test Connection', 'ai-fitness-blog-writer'); ?>
                            </button>
                            <p class="description">
                                <?php printf(
                                    __('Get your free API key from <a href="%s" target="_blank">Pexels</a> to automatically fetch featured images.', 'ai-fitness-blog-writer'),
                                    'https://www.pexels.com/api/'
                                ); ?>
                            </p>
                            <div id="pexels-test-result" class="afbw-test-result"></div>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Content Settings Section -->
            <div class="afbw-settings-section">
                <h2><?php _e('Content Settings', 'ai-fitness-blog-writer'); ?></h2>
                <p class="description"><?php _e('Configure default settings for generated blog posts.', 'ai-fitness-blog-writer'); ?></p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_word_count"><?php _e('Default Word Count', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $word_counts = array(300, 500, 800, 1000, 1500, 2000);
                            $selected_count = $settings['default_word_count'] ?? 800;
                            ?>
                            <select id="default_word_count" name="afbw_settings[default_word_count]">
                                <?php foreach ($word_counts as $count): ?>
                                    <option value="<?php echo $count; ?>" <?php selected($selected_count, $count); ?>>
                                        <?php echo $count; ?> words
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default word count for generated blog posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_publish"><?php _e('Auto Publish Posts', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="auto_publish" name="afbw_settings[auto_publish]" value="1" 
                                   <?php checked($settings['auto_publish'] ?? false, true); ?> />
                            <label for="auto_publish"><?php _e('Automatically publish generated posts', 'ai-fitness-blog-writer'); ?></label>
                            <p class="description"><?php _e('If enabled, generated posts will be published immediately. Otherwise, they will be saved as drafts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_set_featured_image"><?php _e('Auto Set Featured Image', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="auto_set_featured_image" name="afbw_settings[auto_set_featured_image]" value="1" 
                                   <?php checked($settings['auto_set_featured_image'] ?? true, true); ?> />
                            <label for="auto_set_featured_image"><?php _e('Automatically set featured image from Pexels', 'ai-fitness-blog-writer'); ?></label>
                            <p class="description"><?php _e('If enabled, a relevant image will be automatically fetched from Pexels and set as the featured image.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="post_status"><?php _e('Default Post Status', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $statuses = array(
                                'draft' => __('Draft', 'ai-fitness-blog-writer'),
                                'publish' => __('Published', 'ai-fitness-blog-writer'),
                                'private' => __('Private', 'ai-fitness-blog-writer')
                            );
                            $selected_status = $settings['post_status'] ?? 'draft';
                            ?>
                            <select id="post_status" name="afbw_settings[post_status]">
                                <?php foreach ($statuses as $status_key => $status_label): ?>
                                    <option value="<?php echo esc_attr($status_key); ?>" <?php selected($selected_status, $status_key); ?>>
                                        <?php echo esc_html($status_label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default status for generated posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="post_category"><?php _e('Default Post Category', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $categories = get_categories(array('hide_empty' => false));
                            $selected_category = $settings['post_category'] ?? 1;
                            ?>
                            <select id="post_category" name="afbw_settings[post_category]">
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category->term_id; ?>" <?php selected($selected_category, $category->term_id); ?>>
                                        <?php echo esc_html($category->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default category for generated posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <?php submit_button(__('Save Settings', 'ai-fitness-blog-writer')); ?>
        </form>
        
        <!-- Help Section -->
        <div class="afbw-help-section">
            <h2><?php _e('Getting Started', 'ai-fitness-blog-writer'); ?></h2>
            <div class="afbw-help-content">
                <h3><?php _e('1. Get Your API Keys', 'ai-fitness-blog-writer'); ?></h3>
                <ul>
                    <li><strong>OpenRouter:</strong> <?php printf(__('Sign up at <a href="%s" target="_blank">OpenRouter.ai</a> and get your API key. DeepSeek models are free!', 'ai-fitness-blog-writer'), 'https://openrouter.ai/keys'); ?></li>
                    <li><strong>Pexels:</strong> <?php printf(__('Sign up at <a href="%s" target="_blank">Pexels</a> and get your free API key for images.', 'ai-fitness-blog-writer'), 'https://www.pexels.com/api/'); ?></li>
                </ul>
                
                <h3><?php _e('2. Configure Settings', 'ai-fitness-blog-writer'); ?></h3>
                <p><?php _e('Enter your API keys above and configure your preferred settings for content generation.', 'ai-fitness-blog-writer'); ?></p>
                
                <h3><?php _e('3. Start Generating Content', 'ai-fitness-blog-writer'); ?></h3>
                <p><?php printf(__('Go to the <a href="%s">Generate Post</a> page to start creating AI-powered fitness blog posts!', 'ai-fitness-blog-writer'), admin_url('admin.php?page=ai-fitness-blog-writer')); ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.afbw-settings-wrap {
    max-width: 1000px;
}

.afbw-settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.afbw-settings-section h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.afbw-test-result {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    display: none;
}

.afbw-test-result.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.afbw-test-result.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.afbw-help-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.afbw-help-content h3 {
    color: #495057;
    margin-top: 20px;
}

.afbw-help-content h3:first-child {
    margin-top: 0;
}

.afbw-help-content ul {
    margin-left: 20px;
}

.afbw-help-content li {
    margin-bottom: 8px;
}
</style>
