<?php
/**
 * Settings page template for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit'])) {
    check_admin_referer('afbw_settings_nonce');
    
    // Update settings
    $settings = array();
    $settings['openrouter_api_key'] = sanitize_text_field($_POST['afbw_settings']['openrouter_api_key']);
    $settings['openrouter_model'] = sanitize_text_field($_POST['afbw_settings']['openrouter_model']);
    $settings['pexels_api_key'] = sanitize_text_field($_POST['afbw_settings']['pexels_api_key']);
    $settings['default_word_count'] = absint($_POST['afbw_settings']['default_word_count']);
    $settings['auto_publish'] = isset($_POST['afbw_settings']['auto_publish']) ? true : false;
    $settings['auto_set_featured_image'] = isset($_POST['afbw_settings']['auto_set_featured_image']) ? true : false;
    $settings['post_status'] = sanitize_text_field($_POST['afbw_settings']['post_status']);
    $settings['post_category'] = absint($_POST['afbw_settings']['post_category']);

    // Auto-posting settings
    $settings['auto_posting_enabled'] = isset($_POST['afbw_settings']['auto_posting_enabled']) ? true : false;
    $settings['auto_posting_frequency'] = absint($_POST['afbw_settings']['auto_posting_frequency']);
    $settings['auto_posting_start_time'] = sanitize_text_field($_POST['afbw_settings']['auto_posting_start_time']);
    $settings['auto_posting_end_time'] = sanitize_text_field($_POST['afbw_settings']['auto_posting_end_time']);
    $settings['auto_posting_min_interval'] = absint($_POST['afbw_settings']['auto_posting_min_interval']);
    $settings['auto_posting_randomize'] = isset($_POST['afbw_settings']['auto_posting_randomize']) ? true : false;
    $settings['auto_posting_categories'] = isset($_POST['afbw_settings']['auto_posting_categories']) ?
        array_map('sanitize_text_field', $_POST['afbw_settings']['auto_posting_categories']) : array('general');
    $settings['auto_posting_word_count_min'] = absint($_POST['afbw_settings']['auto_posting_word_count_min']);
    $settings['auto_posting_word_count_max'] = absint($_POST['afbw_settings']['auto_posting_word_count_max']);
    
    update_option('afbw_settings', $settings);
    
    echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'ai-fitness-blog-writer') . '</p></div>';
}

$settings = get_option('afbw_settings', array());
?>

<div class="wrap afbw-settings-wrap">
    <h1><?php _e('AI Fitness Blog Writer Settings', 'ai-fitness-blog-writer'); ?></h1>
    
    <div class="afbw-settings-container">
        <form method="post" action="">
            <?php wp_nonce_field('afbw_settings_nonce'); ?>
            
            <!-- API Configuration Section -->
            <div class="afbw-settings-section">
                <h2><?php _e('API Configuration', 'ai-fitness-blog-writer'); ?></h2>
                <p class="description"><?php _e('Configure your API keys for OpenRouter.ai and Pexels to enable content generation and image fetching.', 'ai-fitness-blog-writer'); ?></p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="openrouter_api_key"><?php _e('OpenRouter API Key', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="openrouter_api_key" name="afbw_settings[openrouter_api_key]" 
                                   value="<?php echo esc_attr($settings['openrouter_api_key'] ?? ''); ?>" class="regular-text" />
                            <button type="button" class="button button-secondary" id="test-openrouter-btn" style="margin-left: 10px;">
                                <?php _e('Test Connection', 'ai-fitness-blog-writer'); ?>
                            </button>
                            <p class="description">
                                <?php printf(
                                    __('Get your API key from <a href="%s" target="_blank">OpenRouter.ai</a>. DeepSeek models are free to use.', 'ai-fitness-blog-writer'),
                                    'https://openrouter.ai/keys'
                                ); ?>
                            </p>
                            <div id="openrouter-test-result" class="afbw-test-result"></div>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="openrouter_model"><?php _e('OpenRouter Model', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $models = array(
                                'deepseek/deepseek-chat' => 'DeepSeek Chat (Free)',
                                'deepseek/deepseek-r1' => 'DeepSeek R1 (Free)',
                                'openai/gpt-3.5-turbo' => 'GPT-3.5 Turbo',
                                'openai/gpt-4' => 'GPT-4',
                                'anthropic/claude-3-haiku' => 'Claude 3 Haiku',
                                'meta-llama/llama-3.1-8b-instruct' => 'Llama 3.1 8B'
                            );
                            $selected_model = $settings['openrouter_model'] ?? 'deepseek/deepseek-chat';
                            ?>
                            <select id="openrouter_model" name="afbw_settings[openrouter_model]" class="regular-text">
                                <?php foreach ($models as $model_id => $model_name): ?>
                                    <option value="<?php echo esc_attr($model_id); ?>" <?php selected($selected_model, $model_id); ?>>
                                        <?php echo esc_html($model_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Select the AI model to use for content generation. DeepSeek models are free.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="pexels_api_key"><?php _e('Pexels API Key', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="pexels_api_key" name="afbw_settings[pexels_api_key]" 
                                   value="<?php echo esc_attr($settings['pexels_api_key'] ?? ''); ?>" class="regular-text" />
                            <button type="button" class="button button-secondary" id="test-pexels-btn" style="margin-left: 10px;">
                                <?php _e('Test Connection', 'ai-fitness-blog-writer'); ?>
                            </button>
                            <p class="description">
                                <?php printf(
                                    __('Get your free API key from <a href="%s" target="_blank">Pexels</a> to automatically fetch featured images.', 'ai-fitness-blog-writer'),
                                    'https://www.pexels.com/api/'
                                ); ?>
                            </p>
                            <div id="pexels-test-result" class="afbw-test-result"></div>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Content Settings Section -->
            <div class="afbw-settings-section">
                <h2><?php _e('Content Settings', 'ai-fitness-blog-writer'); ?></h2>
                <p class="description"><?php _e('Configure default settings for generated blog posts.', 'ai-fitness-blog-writer'); ?></p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_word_count"><?php _e('Default Word Count', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $word_counts = array(300, 500, 800, 1000, 1500, 2000);
                            $selected_count = $settings['default_word_count'] ?? 800;
                            ?>
                            <select id="default_word_count" name="afbw_settings[default_word_count]">
                                <?php foreach ($word_counts as $count): ?>
                                    <option value="<?php echo $count; ?>" <?php selected($selected_count, $count); ?>>
                                        <?php echo $count; ?> words
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default word count for generated blog posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_publish"><?php _e('Auto Publish Posts', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="auto_publish" name="afbw_settings[auto_publish]" value="1" 
                                   <?php checked($settings['auto_publish'] ?? false, true); ?> />
                            <label for="auto_publish"><?php _e('Automatically publish generated posts', 'ai-fitness-blog-writer'); ?></label>
                            <p class="description"><?php _e('If enabled, generated posts will be published immediately. Otherwise, they will be saved as drafts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="auto_set_featured_image"><?php _e('Auto Set Featured Image', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="auto_set_featured_image" name="afbw_settings[auto_set_featured_image]" value="1" 
                                   <?php checked($settings['auto_set_featured_image'] ?? true, true); ?> />
                            <label for="auto_set_featured_image"><?php _e('Automatically set featured image from Pexels', 'ai-fitness-blog-writer'); ?></label>
                            <p class="description"><?php _e('If enabled, a relevant image will be automatically fetched from Pexels and set as the featured image.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="post_status"><?php _e('Default Post Status', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $statuses = array(
                                'draft' => __('Draft', 'ai-fitness-blog-writer'),
                                'publish' => __('Published', 'ai-fitness-blog-writer'),
                                'private' => __('Private', 'ai-fitness-blog-writer')
                            );
                            $selected_status = $settings['post_status'] ?? 'draft';
                            ?>
                            <select id="post_status" name="afbw_settings[post_status]">
                                <?php foreach ($statuses as $status_key => $status_label): ?>
                                    <option value="<?php echo esc_attr($status_key); ?>" <?php selected($selected_status, $status_key); ?>>
                                        <?php echo esc_html($status_label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default status for generated posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="post_category"><?php _e('Default Post Category', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            $categories = get_categories(array('hide_empty' => false));
                            $selected_category = $settings['post_category'] ?? 1;
                            ?>
                            <select id="post_category" name="afbw_settings[post_category]">
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category->term_id; ?>" <?php selected($selected_category, $category->term_id); ?>>
                                        <?php echo esc_html($category->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default category for generated posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Auto-Posting Settings -->
            <div class="afbw-settings-section">
                <h2><?php _e('Auto-Posting Settings', 'ai-fitness-blog-writer'); ?></h2>
                <p><?php _e('Configure automatic post generation to create 3-5 fitness blog posts per day.', 'ai-fitness-blog-writer'); ?></p>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="auto_posting_enabled"><?php _e('Enable Auto-Posting', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="auto_posting_enabled" name="afbw_settings[auto_posting_enabled]" value="1"
                                   <?php checked($settings['auto_posting_enabled'] ?? false, true); ?> />
                            <label for="auto_posting_enabled"><?php _e('Automatically generate and publish posts', 'ai-fitness-blog-writer'); ?></label>
                            <p class="description"><?php _e('When enabled, the system will automatically generate fitness blog posts throughout the day.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="auto_posting_frequency"><?php _e('Posts Per Day', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <select id="auto_posting_frequency" name="afbw_settings[auto_posting_frequency]">
                                <?php
                                $frequency_options = array(1 => '1', 2 => '2', 3 => '3', 4 => '4', 5 => '5', 6 => '6', 7 => '7', 8 => '8');
                                $selected_frequency = $settings['auto_posting_frequency'] ?? 4;
                                foreach ($frequency_options as $value => $label):
                                ?>
                                    <option value="<?php echo $value; ?>" <?php selected($selected_frequency, $value); ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Number of posts to generate automatically per day.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="auto_posting_start_time"><?php _e('Start Time', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="time" id="auto_posting_start_time" name="afbw_settings[auto_posting_start_time]"
                                   value="<?php echo esc_attr($settings['auto_posting_start_time'] ?? '08:00'); ?>" />
                            <p class="description"><?php _e('Time to start generating posts each day.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="auto_posting_end_time"><?php _e('End Time', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="time" id="auto_posting_end_time" name="afbw_settings[auto_posting_end_time]"
                                   value="<?php echo esc_attr($settings['auto_posting_end_time'] ?? '18:00'); ?>" />
                            <p class="description"><?php _e('Time to stop generating posts each day.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="auto_posting_min_interval"><?php _e('Minimum Interval (Hours)', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <select id="auto_posting_min_interval" name="afbw_settings[auto_posting_min_interval]">
                                <?php
                                $interval_options = array(1 => '1', 2 => '2', 3 => '3', 4 => '4', 6 => '6', 8 => '8', 12 => '12');
                                $selected_interval = $settings['auto_posting_min_interval'] ?? 2;
                                foreach ($interval_options as $value => $label):
                                ?>
                                    <option value="<?php echo $value; ?>" <?php selected($selected_interval, $value); ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Minimum hours between auto-generated posts.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="auto_posting_randomize"><?php _e('Randomize Timing', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="auto_posting_randomize" name="afbw_settings[auto_posting_randomize]" value="1"
                                   <?php checked($settings['auto_posting_randomize'] ?? true, true); ?> />
                            <label for="auto_posting_randomize"><?php _e('Add randomness to posting times', 'ai-fitness-blog-writer'); ?></label>
                            <p class="description"><?php _e('Makes posting times more natural and less predictable.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label><?php _e('Topic Categories', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <?php
                            require_once AFBW_PLUGIN_PATH . 'includes/class-topic-pool.php';
                            $available_categories = AFBW_Topic_Pool::get_categories();
                            $selected_categories = $settings['auto_posting_categories'] ?? array('general');
                            ?>
                            <fieldset>
                                <legend class="screen-reader-text"><?php _e('Select topic categories for auto-posting', 'ai-fitness-blog-writer'); ?></legend>
                                <label>
                                    <input type="checkbox" name="afbw_settings[auto_posting_categories][]" value="general"
                                           <?php checked(in_array('general', $selected_categories), true); ?> />
                                    <?php _e('All Categories (Random)', 'ai-fitness-blog-writer'); ?>
                                </label><br>
                                <?php foreach ($available_categories as $cat_key => $cat_label): ?>
                                    <label>
                                        <input type="checkbox" name="afbw_settings[auto_posting_categories][]" value="<?php echo esc_attr($cat_key); ?>"
                                               <?php checked(in_array($cat_key, $selected_categories), true); ?> />
                                        <?php echo esc_html($cat_label); ?>
                                    </label><br>
                                <?php endforeach; ?>
                            </fieldset>
                            <p class="description"><?php _e('Select which fitness topic categories to use for auto-posting. If "All Categories" is selected, others will be ignored.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="auto_posting_word_count_min"><?php _e('Word Count Range', 'ai-fitness-blog-writer'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="auto_posting_word_count_min" name="afbw_settings[auto_posting_word_count_min]"
                                   value="<?php echo esc_attr($settings['auto_posting_word_count_min'] ?? 600); ?>"
                                   min="300" max="2000" step="50" style="width: 100px;" />
                            <span><?php _e('to', 'ai-fitness-blog-writer'); ?></span>
                            <input type="number" id="auto_posting_word_count_max" name="afbw_settings[auto_posting_word_count_max]"
                                   value="<?php echo esc_attr($settings['auto_posting_word_count_max'] ?? 1200); ?>"
                                   min="300" max="2000" step="50" style="width: 100px;" />
                            <span><?php _e('words', 'ai-fitness-blog-writer'); ?></span>
                            <p class="description"><?php _e('Random word count will be selected within this range for each auto-generated post.', 'ai-fitness-blog-writer'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <?php submit_button(__('Save Settings', 'ai-fitness-blog-writer')); ?>
        </form>
        
        <!-- Help Section -->
        <div class="afbw-help-section">
            <h2><?php _e('Getting Started', 'ai-fitness-blog-writer'); ?></h2>
            <div class="afbw-help-content">
                <h3><?php _e('1. Get Your API Keys', 'ai-fitness-blog-writer'); ?></h3>
                <ul>
                    <li><strong>OpenRouter:</strong> <?php printf(__('Sign up at <a href="%s" target="_blank">OpenRouter.ai</a> and get your API key. DeepSeek models are free!', 'ai-fitness-blog-writer'), 'https://openrouter.ai/keys'); ?></li>
                    <li><strong>Pexels:</strong> <?php printf(__('Sign up at <a href="%s" target="_blank">Pexels</a> and get your free API key for images.', 'ai-fitness-blog-writer'), 'https://www.pexels.com/api/'); ?></li>
                </ul>
                
                <h3><?php _e('2. Configure Settings', 'ai-fitness-blog-writer'); ?></h3>
                <p><?php _e('Enter your API keys above and configure your preferred settings for content generation.', 'ai-fitness-blog-writer'); ?></p>
                
                <h3><?php _e('3. Start Generating Content', 'ai-fitness-blog-writer'); ?></h3>
                <p><?php printf(__('Go to the <a href="%s">Generate Post</a> page to start creating AI-powered fitness blog posts!', 'ai-fitness-blog-writer'), admin_url('admin.php?page=ai-fitness-blog-writer')); ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.afbw-settings-wrap {
    max-width: 1000px;
}

.afbw-settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.afbw-settings-section h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.afbw-test-result {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    display: none;
}

.afbw-test-result.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.afbw-test-result.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.afbw-help-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.afbw-help-content h3 {
    color: #495057;
    margin-top: 20px;
}

.afbw-help-content h3:first-child {
    margin-top: 0;
}

.afbw-help-content ul {
    margin-left: 20px;
}

.afbw-help-content li {
    margin-bottom: 8px;
}
</style>
