<?php
/**
 * Generated posts page template for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle bulk actions
if (isset($_POST['action']) && $_POST['action'] === 'bulk_delete' && isset($_POST['generated_posts'])) {
    check_admin_referer('afbw_bulk_action_nonce');
    
    global $wpdb;
    $deleted_count = 0;
    
    foreach ($_POST['generated_posts'] as $post_id) {
        $post_id = absint($post_id);
        
        // Get the post data first
        $generated_post = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}afbw_generated_posts WHERE id = %d",
                $post_id
            )
        );
        
        if ($generated_post) {
            // Delete the WordPress post if it exists
            if ($generated_post->post_id) {
                wp_delete_post($generated_post->post_id, true);
            }
            
            // Delete the generated post record
            $result = $wpdb->delete(
                $wpdb->prefix . 'afbw_generated_posts',
                array('id' => $post_id),
                array('%d')
            );
            
            if ($result !== false) {
                $deleted_count++;
            }
        }
    }
    
    echo '<div class="notice notice-success is-dismissible"><p>' . 
         sprintf(__('%d generated posts deleted successfully.', 'ai-fitness-blog-writer'), $deleted_count) . 
         '</p></div>';
}

// Get generated posts from database
global $wpdb;
$table_name = $wpdb->prefix . 'afbw_generated_posts';

// Pagination
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, absint($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Get total count
$total_posts = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
$total_pages = ceil($total_posts / $per_page);

// Get posts for current page
$generated_posts = $wpdb->get_results(
    $wpdb->prepare(
        "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d OFFSET %d",
        $per_page,
        $offset
    )
);
?>

<div class="wrap afbw-generated-posts-wrap">
    <h1><?php _e('Generated Posts', 'ai-fitness-blog-writer'); ?></h1>
    <p class="description"><?php _e('Manage your AI-generated fitness blog posts.', 'ai-fitness-blog-writer'); ?></p>
    
    <?php if (empty($generated_posts)): ?>
        <div class="afbw-empty-state">
            <h2><?php _e('No Generated Posts Yet', 'ai-fitness-blog-writer'); ?></h2>
            <p><?php _e('You haven\'t generated any blog posts yet. Start creating amazing fitness content!', 'ai-fitness-blog-writer'); ?></p>
            <a href="<?php echo admin_url('admin.php?page=ai-fitness-blog-writer'); ?>" class="button button-primary">
                <?php _e('Generate Your First Post', 'ai-fitness-blog-writer'); ?>
            </a>
        </div>
    <?php else: ?>
        <form method="post" action="">
            <?php wp_nonce_field('afbw_bulk_action_nonce'); ?>
            
            <div class="tablenav top">
                <div class="alignleft actions bulkactions">
                    <select name="action">
                        <option value="-1"><?php _e('Bulk Actions', 'ai-fitness-blog-writer'); ?></option>
                        <option value="bulk_delete"><?php _e('Delete', 'ai-fitness-blog-writer'); ?></option>
                    </select>
                    <input type="submit" class="button action" value="<?php _e('Apply', 'ai-fitness-blog-writer'); ?>" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete the selected posts?', 'ai-fitness-blog-writer'); ?>');">
                </div>
                
                <div class="tablenav-pages">
                    <?php if ($total_pages > 1): ?>
                        <span class="displaying-num">
                            <?php printf(__('%d items', 'ai-fitness-blog-writer'), $total_posts); ?>
                        </span>
                        <?php
                        $page_links = paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $current_page
                        ));
                        echo $page_links;
                        ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <td class="manage-column column-cb check-column">
                            <input type="checkbox" id="cb-select-all-1">
                        </td>
                        <th class="manage-column column-topic"><?php _e('Topic', 'ai-fitness-blog-writer'); ?></th>
                        <th class="manage-column column-audience"><?php _e('Audience', 'ai-fitness-blog-writer'); ?></th>
                        <th class="manage-column column-words"><?php _e('Words', 'ai-fitness-blog-writer'); ?></th>
                        <th class="manage-column column-status"><?php _e('Status', 'ai-fitness-blog-writer'); ?></th>
                        <th class="manage-column column-created"><?php _e('Created', 'ai-fitness-blog-writer'); ?></th>
                        <th class="manage-column column-actions"><?php _e('Actions', 'ai-fitness-blog-writer'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($generated_posts as $post): ?>
                        <tr>
                            <th class="check-column">
                                <input type="checkbox" name="generated_posts[]" value="<?php echo $post->id; ?>">
                            </th>
                            <td class="column-topic">
                                <strong><?php echo esc_html($post->topic); ?></strong>
                                <?php if ($post->keywords): ?>
                                    <br><small class="description">
                                        <?php _e('Keywords:', 'ai-fitness-blog-writer'); ?> 
                                        <?php echo esc_html($post->keywords); ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td class="column-audience">
                                <?php echo esc_html($post->target_audience ?: __('General', 'ai-fitness-blog-writer')); ?>
                            </td>
                            <td class="column-words">
                                <?php echo number_format($post->word_count); ?>
                            </td>
                            <td class="column-status">
                                <?php
                                $status_class = '';
                                $status_text = '';
                                
                                switch ($post->status) {
                                    case 'generated':
                                        $status_class = 'afbw-status-generated';
                                        $status_text = __('Generated', 'ai-fitness-blog-writer');
                                        break;
                                    case 'draft':
                                        $status_class = 'afbw-status-draft';
                                        $status_text = __('Draft', 'ai-fitness-blog-writer');
                                        break;
                                    case 'published':
                                        $status_class = 'afbw-status-published';
                                        $status_text = __('Published', 'ai-fitness-blog-writer');
                                        break;
                                    default:
                                        $status_class = 'afbw-status-unknown';
                                        $status_text = __('Unknown', 'ai-fitness-blog-writer');
                                }
                                ?>
                                <span class="afbw-status <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td class="column-created">
                                <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($post->created_at)); ?>
                            </td>
                            <td class="column-actions">
                                <div class="row-actions">
                                    <span class="view">
                                        <a href="#" class="afbw-preview-content" data-id="<?php echo $post->id; ?>">
                                            <?php _e('Preview', 'ai-fitness-blog-writer'); ?>
                                        </a>
                                    </span>
                                    
                                    <?php if ($post->post_id): ?>
                                        <span class="edit"> | 
                                            <a href="<?php echo admin_url('post.php?post=' . $post->post_id . '&action=edit'); ?>">
                                                <?php _e('Edit Post', 'ai-fitness-blog-writer'); ?>
                                            </a>
                                        </span>
                                        <span class="view"> | 
                                            <a href="<?php echo get_permalink($post->post_id); ?>" target="_blank">
                                                <?php _e('View Post', 'ai-fitness-blog-writer'); ?>
                                            </a>
                                        </span>
                                    <?php else: ?>
                                        <span class="publish"> | 
                                            <a href="#" class="afbw-publish-post" data-id="<?php echo $post->id; ?>">
                                                <?php _e('Publish', 'ai-fitness-blog-writer'); ?>
                                            </a>
                                        </span>
                                    <?php endif; ?>
                                    
                                    <span class="delete"> | 
                                        <a href="#" class="afbw-delete-post" data-id="<?php echo $post->id; ?>" 
                                           onclick="return confirm('<?php _e('Are you sure you want to delete this generated post?', 'ai-fitness-blog-writer'); ?>');">
                                            <?php _e('Delete', 'ai-fitness-blog-writer'); ?>
                                        </a>
                                    </span>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="tablenav bottom">
                <div class="alignleft actions bulkactions">
                    <select name="action">
                        <option value="-1"><?php _e('Bulk Actions', 'ai-fitness-blog-writer'); ?></option>
                        <option value="bulk_delete"><?php _e('Delete', 'ai-fitness-blog-writer'); ?></option>
                    </select>
                    <input type="submit" class="button action" value="<?php _e('Apply', 'ai-fitness-blog-writer'); ?>" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete the selected posts?', 'ai-fitness-blog-writer'); ?>');">
                </div>
                
                <div class="tablenav-pages">
                    <?php if ($total_pages > 1): ?>
                        <span class="displaying-num">
                            <?php printf(__('%d items', 'ai-fitness-blog-writer'), $total_posts); ?>
                        </span>
                        <?php echo $page_links; ?>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    <?php endif; ?>
</div>

<!-- Preview Modal -->
<div id="afbw-preview-modal" class="afbw-modal" style="display: none;">
    <div class="afbw-modal-content">
        <div class="afbw-modal-header">
            <h2><?php _e('Content Preview', 'ai-fitness-blog-writer'); ?></h2>
            <span class="afbw-modal-close">&times;</span>
        </div>
        <div class="afbw-modal-body">
            <div id="afbw-preview-content-display"></div>
        </div>
        <div class="afbw-modal-footer">
            <button type="button" class="button" id="afbw-close-preview">
                <?php _e('Close', 'ai-fitness-blog-writer'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.afbw-generated-posts-wrap {
    max-width: 1200px;
}

.afbw-empty-state {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.afbw-empty-state h2 {
    color: #666;
    margin-bottom: 10px;
}

.afbw-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.afbw-status-generated {
    background-color: #e1f5fe;
    color: #0277bd;
}

.afbw-status-draft {
    background-color: #fff3e0;
    color: #ef6c00;
}

.afbw-status-published {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.afbw-status-unknown {
    background-color: #f5f5f5;
    color: #666;
}

.column-topic {
    width: 25%;
}

.column-audience {
    width: 15%;
}

.column-words {
    width: 10%;
}

.column-status {
    width: 12%;
}

.column-created {
    width: 15%;
}

.column-actions {
    width: 23%;
}

.afbw-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.afbw-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
}

.afbw-modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.afbw-modal-header h2 {
    margin: 0;
}

.afbw-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.afbw-modal-close:hover {
    color: #000;
}

.afbw-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.afbw-modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

#afbw-preview-content-display {
    line-height: 1.6;
}

#afbw-preview-content-display h1,
#afbw-preview-content-display h2,
#afbw-preview-content-display h3,
#afbw-preview-content-display h4 {
    margin-top: 20px;
    margin-bottom: 10px;
}

#afbw-preview-content-display p {
    margin-bottom: 15px;
}

#afbw-preview-content-display ul,
#afbw-preview-content-display ol {
    margin-bottom: 15px;
    padding-left: 20px;
}
</style>
