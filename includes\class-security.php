<?php
/**
 * Security functionality for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Security {
    
    /**
     * Initialize security measures
     */
    public static function init() {
        add_action('init', array(__CLASS__, 'setup_security_headers'));
        add_filter('wp_kses_allowed_html', array(__CLASS__, 'allow_additional_html_tags'), 10, 2);
        add_action('wp_ajax_nopriv_afbw_generate_content', array(__CLASS__, 'block_unauthorized_ajax'));
        add_action('wp_ajax_nopriv_afbw_publish_post', array(__CLASS__, 'block_unauthorized_ajax'));
        add_action('wp_ajax_nopriv_afbw_save_draft', array(__CLASS__, 'block_unauthorized_ajax'));
        add_action('wp_ajax_nopriv_afbw_test_api_connection', array(__CLASS__, 'block_unauthorized_ajax'));
    }
    
    /**
     * Setup security headers
     */
    public static function setup_security_headers() {
        if (is_admin() && isset($_GET['page']) && strpos($_GET['page'], 'afbw') !== false) {
            // Add security headers for admin pages
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
        }
    }
    
    /**
     * Block unauthorized AJAX requests
     */
    public static function block_unauthorized_ajax() {
        wp_die(__('Unauthorized access', 'ai-fitness-blog-writer'), 403);
    }
    
    /**
     * Verify nonce for AJAX requests
     */
    public static function verify_ajax_nonce($action = 'afbw_nonce') {
        if (!check_ajax_referer($action, 'nonce', false)) {
            wp_send_json_error(__('Security check failed', 'ai-fitness-blog-writer'));
        }
    }
    
    /**
     * Verify user capabilities
     */
    public static function verify_user_capability($capability = 'manage_options') {
        if (!current_user_can($capability)) {
            wp_send_json_error(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }
    }
    
    /**
     * Sanitize blog topic input
     */
    public static function sanitize_topic($topic) {
        $topic = sanitize_text_field($topic);
        $topic = trim($topic);
        
        // Remove any potentially harmful content
        $topic = preg_replace('/[<>"\']/', '', $topic);
        
        // Limit length
        if (strlen($topic) > 200) {
            $topic = substr($topic, 0, 200);
        }
        
        return $topic;
    }
    
    /**
     * Sanitize target audience input
     */
    public static function sanitize_audience($audience) {
        $audience = sanitize_text_field($audience);
        $audience = trim($audience);
        
        // Remove any potentially harmful content
        $audience = preg_replace('/[<>"\']/', '', $audience);
        
        // Limit length
        if (strlen($audience) > 100) {
            $audience = substr($audience, 0, 100);
        }
        
        return $audience;
    }
    
    /**
     * Sanitize keywords input
     */
    public static function sanitize_keywords($keywords) {
        $keywords = sanitize_text_field($keywords);
        $keywords = trim($keywords);
        
        // Remove any potentially harmful content
        $keywords = preg_replace('/[<>"\']/', '', $keywords);
        
        // Limit length
        if (strlen($keywords) > 500) {
            $keywords = substr($keywords, 0, 500);
        }
        
        // Validate comma-separated format
        $keyword_array = array_map('trim', explode(',', $keywords));
        $keyword_array = array_filter($keyword_array, function($keyword) {
            return !empty($keyword) && strlen($keyword) <= 50;
        });
        
        return implode(', ', $keyword_array);
    }
    
    /**
     * Validate word count
     */
    public static function validate_word_count($word_count) {
        $word_count = absint($word_count);
        
        // Ensure word count is within allowed range
        $allowed_counts = array(300, 500, 800, 1000, 1500, 2000);
        
        if (!in_array($word_count, $allowed_counts)) {
            return 800; // Default fallback
        }
        
        return $word_count;
    }
    
    /**
     * Sanitize API key
     */
    public static function sanitize_api_key($api_key) {
        $api_key = sanitize_text_field($api_key);
        $api_key = trim($api_key);
        
        // Remove any whitespace and special characters that shouldn't be in API keys
        $api_key = preg_replace('/[^a-zA-Z0-9\-_.]/', '', $api_key);
        
        // Limit length (most API keys are under 100 characters)
        if (strlen($api_key) > 200) {
            $api_key = substr($api_key, 0, 200);
        }
        
        return $api_key;
    }
    
    /**
     * Validate generated content before saving
     */
    public static function validate_generated_content($content) {
        if (empty($content)) {
            return new WP_Error('empty_content', __('Generated content is empty', 'ai-fitness-blog-writer'));
        }
        
        // Check content length
        $word_count = str_word_count(strip_tags($content));
        if ($word_count < 50) {
            return new WP_Error('content_too_short', __('Generated content is too short', 'ai-fitness-blog-writer'));
        }
        
        if ($word_count > 5000) {
            return new WP_Error('content_too_long', __('Generated content is too long', 'ai-fitness-blog-writer'));
        }
        
        // Check for potentially harmful content
        $suspicious_patterns = array(
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onclick=/i',
            '/onerror=/i'
        );
        
        foreach ($suspicious_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return new WP_Error('suspicious_content', __('Content contains potentially harmful elements', 'ai-fitness-blog-writer'));
            }
        }
        
        return true;
    }
    
    /**
     * Rate limiting for API requests
     */
    public static function check_rate_limit($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $transient_key = 'afbw_rate_limit_' . $user_id;
        $requests = get_transient($transient_key);
        
        if ($requests === false) {
            // First request in the time window
            set_transient($transient_key, 1, HOUR_IN_SECONDS);
            return true;
        }
        
        // Check if user has exceeded rate limit (10 requests per hour)
        if ($requests >= 10) {
            return new WP_Error('rate_limit_exceeded', __('Rate limit exceeded. Please try again later.', 'ai-fitness-blog-writer'));
        }
        
        // Increment request count
        set_transient($transient_key, $requests + 1, HOUR_IN_SECONDS);
        return true;
    }
    
    /**
     * Log security events
     */
    public static function log_security_event($event, $details = array()) {
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            return;
        }
        
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'user_ip' => self::get_user_ip(),
            'event' => $event,
            'details' => $details
        );
        
        error_log('AFBW Security Event: ' . json_encode($log_entry));
    }
    
    /**
     * Get user IP address
     */
    private static function get_user_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Allow additional HTML tags for content
     */
    public static function allow_additional_html_tags($tags, $context) {
        if ($context === 'post') {
            // Allow additional tags for blog post content
            $additional_tags = array(
                'h1' => array(),
                'h2' => array(),
                'h3' => array(),
                'h4' => array(),
                'h5' => array(),
                'h6' => array(),
                'blockquote' => array(),
                'code' => array(),
                'pre' => array(),
                'mark' => array(),
                'small' => array(),
                'sub' => array(),
                'sup' => array()
            );
            
            $tags = array_merge($tags, $additional_tags);
        }
        
        return $tags;
    }
    
    /**
     * Validate image URL from Pexels
     */
    public static function validate_image_url($url) {
        // Check if URL is from Pexels
        if (strpos($url, 'images.pexels.com') === false) {
            return new WP_Error('invalid_image_source', __('Image must be from Pexels', 'ai-fitness-blog-writer'));
        }
        
        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return new WP_Error('invalid_url', __('Invalid image URL', 'ai-fitness-blog-writer'));
        }
        
        // Check if URL uses HTTPS
        if (strpos($url, 'https://') !== 0) {
            return new WP_Error('insecure_url', __('Image URL must use HTTPS', 'ai-fitness-blog-writer'));
        }
        
        return true;
    }
    
    /**
     * Clean up old generated posts (run via cron)
     */
    public static function cleanup_old_posts() {
        global $wpdb;
        
        // Delete generated posts older than 30 days that haven't been published
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->prefix}afbw_generated_posts 
                WHERE status = 'generated' 
                AND created_at < %s",
                date('Y-m-d H:i:s', strtotime('-30 days'))
            )
        );
    }
}

// Initialize security
AFBW_Security::init();
