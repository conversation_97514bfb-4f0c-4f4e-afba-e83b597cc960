<?php
/**
 * Plugin Name: AI Fitness Blog Writer
 * Plugin URI: https://github.com/your-username/ai-fitness-blog-writer
 * Description: Generate and publish fitness blog posts using OpenRouter.ai's DeepSeek R1 model with automatic featured images from Pexels.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ai-fitness-blog-writer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('AFBW_VERSION', '1.0.0');
define('AFBW_PLUGIN_URL', plugin_dir_url(__FILE__));
define('AFBW_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('AFBW_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main AI Fitness Blog Writer Plugin Class
 */
class AI_Fitness_Blog_Writer {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('AI_Fitness_Blog_Writer', 'uninstall'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('ai-fitness-blog-writer', false, dirname(AFBW_PLUGIN_BASENAME) . '/languages');
        
        // Initialize admin functionality
        if (is_admin()) {
            $this->init_admin();
        }
        
        // Initialize AJAX handlers
        $this->init_ajax();
    }
    
    /**
     * Initialize admin functionality
     */
    private function init_admin() {
        require_once AFBW_PLUGIN_PATH . 'includes/class-security.php';
        require_once AFBW_PLUGIN_PATH . 'includes/class-admin.php';
        require_once AFBW_PLUGIN_PATH . 'includes/class-settings.php';
        require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';

        new AFBW_Admin();
        new AFBW_Settings();
        new AFBW_Generator();
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax() {
        require_once AFBW_PLUGIN_PATH . 'includes/class-ajax.php';
        new AFBW_Ajax();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove plugin options
        delete_option('afbw_settings');
        delete_option('afbw_version');
        
        // Drop custom tables
        global $wpdb;
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}afbw_generated_posts");
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$wpdb->prefix}afbw_generated_posts (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) DEFAULT NULL,
            topic varchar(255) NOT NULL,
            target_audience varchar(255) DEFAULT NULL,
            keywords text DEFAULT NULL,
            word_count int(11) DEFAULT NULL,
            generated_content longtext DEFAULT NULL,
            featured_image_url varchar(500) DEFAULT NULL,
            status varchar(20) DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY post_id (post_id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_settings = array(
            'openrouter_api_key' => '',
            'openrouter_model' => 'deepseek/deepseek-chat',
            'pexels_api_key' => '',
            'default_word_count' => 800,
            'auto_publish' => false,
            'auto_set_featured_image' => true,
            'post_status' => 'draft',
            'post_category' => 1
        );
        
        add_option('afbw_settings', $default_settings);
        add_option('afbw_version', AFBW_VERSION);
    }
}

// Initialize the plugin
AI_Fitness_Blog_Writer::get_instance();
