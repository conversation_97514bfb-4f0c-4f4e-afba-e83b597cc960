<?php
/**
 * Test script for AI Fitness Blog Writer Auto-Posting System
 * 
 * This script can be used to test the auto-posting functionality
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Auto-Posting System
 */
function afbw_test_auto_posting() {
    echo "<h2>AI Fitness Blog Writer - Auto-Posting System Test</h2>\n";
    
    // Check if plugin is active
    if (!class_exists('AI_Fitness_Blog_Writer')) {
        echo "<p style='color: red;'>❌ Plugin not active</p>\n";
        return;
    }
    
    echo "<p style='color: green;'>✅ Plugin is active</p>\n";
    
    // Check settings
    $settings = get_option('afbw_settings', array());
    
    echo "<h3>Configuration Check</h3>\n";
    
    // API Keys
    if (!empty($settings['openrouter_api_key'])) {
        echo "<p style='color: green;'>✅ OpenRouter API key configured</p>\n";
    } else {
        echo "<p style='color: red;'>❌ OpenRouter API key missing</p>\n";
    }
    
    if (!empty($settings['pexels_api_key'])) {
        echo "<p style='color: green;'>✅ Pexels API key configured</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ Pexels API key missing (optional)</p>\n";
    }
    
    // Auto-posting settings
    echo "<h3>Auto-Posting Settings</h3>\n";
    echo "<ul>\n";
    echo "<li>Enabled: " . ($settings['auto_posting_enabled'] ?? false ? 'Yes' : 'No') . "</li>\n";
    echo "<li>Posts per day: " . ($settings['auto_posting_frequency'] ?? 4) . "</li>\n";
    echo "<li>Start time: " . ($settings['auto_posting_start_time'] ?? '08:00') . "</li>\n";
    echo "<li>End time: " . ($settings['auto_posting_end_time'] ?? '18:00') . "</li>\n";
    echo "<li>Min interval: " . ($settings['auto_posting_min_interval'] ?? 2) . " hours</li>\n";
    echo "<li>Randomize: " . ($settings['auto_posting_randomize'] ?? true ? 'Yes' : 'No') . "</li>\n";
    echo "</ul>\n";
    
    // Check cron jobs
    echo "<h3>Cron Jobs Status</h3>\n";
    
    $auto_posting_next = wp_next_scheduled('afbw_auto_posting_check');
    $daily_cleanup_next = wp_next_scheduled('afbw_daily_cleanup');
    
    if ($auto_posting_next) {
        echo "<p style='color: green;'>✅ Auto-posting cron scheduled for: " . date('Y-m-d H:i:s', $auto_posting_next) . "</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Auto-posting cron not scheduled</p>\n";
    }
    
    if ($daily_cleanup_next) {
        echo "<p style='color: green;'>✅ Daily cleanup cron scheduled for: " . date('Y-m-d H:i:s', $daily_cleanup_next) . "</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Daily cleanup cron not scheduled</p>\n";
    }
    
    // Check database tables
    echo "<h3>Database Tables</h3>\n";
    
    global $wpdb;
    
    $generated_posts_table = $wpdb->prefix . 'afbw_generated_posts';
    $logs_table = $wpdb->prefix . 'afbw_auto_posting_logs';
    
    $generated_posts_exists = $wpdb->get_var("SHOW TABLES LIKE '{$generated_posts_table}'") == $generated_posts_table;
    $logs_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") == $logs_table;
    
    if ($generated_posts_exists) {
        echo "<p style='color: green;'>✅ Generated posts table exists</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Generated posts table missing</p>\n";
    }
    
    if ($logs_table_exists) {
        echo "<p style='color: green;'>✅ Auto-posting logs table exists</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Auto-posting logs table missing</p>\n";
    }
    
    // Check topic pool
    echo "<h3>Topic Pool</h3>\n";
    
    if (class_exists('AFBW_Topic_Pool')) {
        $topics = AFBW_Topic_Pool::get_all_topics();
        $total_topics = 0;
        foreach ($topics as $category => $category_topics) {
            $count = count($category_topics);
            $total_topics += $count;
            echo "<p>📝 {$category}: {$count} topics</p>\n";
        }
        echo "<p style='color: green;'>✅ Total topics available: {$total_topics}</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Topic pool class not found</p>\n";
    }
    
    // Test topic selection
    echo "<h3>Topic Selection Test</h3>\n";
    
    if (class_exists('AFBW_Topic_Pool')) {
        $random_topic = AFBW_Topic_Pool::get_random_topic();
        if ($random_topic) {
            echo "<p style='color: green;'>✅ Random topic selected:</p>\n";
            echo "<ul>\n";
            echo "<li><strong>Topic:</strong> " . esc_html($random_topic['topic']) . "</li>\n";
            echo "<li><strong>Audience:</strong> " . esc_html($random_topic['audience']) . "</li>\n";
            echo "<li><strong>Keywords:</strong> " . esc_html($random_topic['keywords']) . "</li>\n";
            echo "<li><strong>Category:</strong> " . esc_html($random_topic['category']) . "</li>\n";
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'>❌ Failed to select random topic</p>\n";
        }
    }
    
    // Check today's posts
    echo "<h3>Today's Auto-Generated Posts</h3>\n";
    
    $today_start = date('Y-m-d 00:00:00');
    $today_end = date('Y-m-d 23:59:59');
    
    $today_count = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}afbw_generated_posts 
            WHERE status = 'auto_published' 
            AND created_at BETWEEN %s AND %s",
            $today_start,
            $today_end
        )
    );
    
    echo "<p>📊 Posts generated today: {$today_count}</p>\n";
    
    $recent_posts = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}afbw_generated_posts 
            WHERE status = 'auto_published' 
            AND created_at BETWEEN %s AND %s
            ORDER BY created_at DESC",
            $today_start,
            $today_end
        )
    );
    
    if (!empty($recent_posts)) {
        echo "<h4>Today's Posts:</h4>\n";
        echo "<ul>\n";
        foreach ($recent_posts as $post) {
            $post_title = $post->post_id ? get_the_title($post->post_id) : $post->topic;
            $post_time = date('H:i:s', strtotime($post->created_at));
            echo "<li>{$post_time} - " . esc_html($post_title) . " ({$post->word_count} words)</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Recent logs
    echo "<h3>Recent Activity Logs</h3>\n";
    
    $recent_logs = $wpdb->get_results(
        "SELECT * FROM {$wpdb->prefix}afbw_auto_posting_logs 
        ORDER BY created_at DESC 
        LIMIT 5"
    );
    
    if (!empty($recent_logs)) {
        echo "<ul>\n";
        foreach ($recent_logs as $log) {
            $log_time = date('M j H:i:s', strtotime($log->created_at));
            $log_class = $log->log_type === 'error' ? 'color: red;' : 'color: green;';
            echo "<li style='{$log_class}'>[{$log_time}] {$log->log_type}: " . esc_html($log->message) . "</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>No logs found</p>\n";
    }
    
    // Manual test button
    echo "<h3>Manual Test</h3>\n";
    echo "<p>You can manually trigger a post generation to test the system:</p>\n";
    echo "<button onclick='testAutoPosting()' class='button button-primary'>Generate Test Post</button>\n";
    
    echo "<script>
    function testAutoPosting() {
        if (!confirm('Generate a test post now? This will count towards your daily limit.')) {
            return;
        }
        
        jQuery.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'afbw_force_generate_post',
                nonce: '" . wp_create_nonce('afbw_nonce') . "'
            },
            success: function(response) {
                if (response.success) {
                    alert('Test post generated successfully! Post ID: ' + response.data.post_id);
                    location.reload();
                } else {
                    alert('Error: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Connection error occurred');
            }
        });
    }
    </script>\n";
    
    echo "<h3>Summary</h3>\n";
    
    $issues = array();
    
    if (empty($settings['openrouter_api_key'])) {
        $issues[] = "OpenRouter API key not configured";
    }
    
    if (!$auto_posting_next) {
        $issues[] = "Auto-posting cron not scheduled";
    }
    
    if (!$generated_posts_exists) {
        $issues[] = "Generated posts table missing";
    }
    
    if (!$logs_table_exists) {
        $issues[] = "Logs table missing";
    }
    
    if (empty($issues)) {
        echo "<p style='color: green; font-weight: bold;'>✅ All systems operational! Auto-posting should work correctly.</p>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Issues found:</p>\n";
        echo "<ul>\n";
        foreach ($issues as $issue) {
            echo "<li style='color: red;'>{$issue}</li>\n";
        }
        echo "</ul>\n";
        echo "<p>Please fix these issues before enabling auto-posting.</p>\n";
    }
}

// Run the test if accessed directly
if (isset($_GET['afbw_test']) && current_user_can('manage_options')) {
    afbw_test_auto_posting();
}
?>
