# AI Fitness Blog Writer - Installation Guide

## Quick Start Guide

### Step 1: Install the Plugin

1. **Download** the plugin files
2. **Upload** to your WordPress site:
   - Via FTP: Upload the `ai-fitness-blog-writer` folder to `/wp-content/plugins/`
   - Via WordPress Admin: Go to Plugins > Add New > Upload Plugin
3. **Activate** the plugin in your WordPress admin panel

### Step 2: Get Your API Keys

#### OpenRouter.ai (Required for AI content generation)
1. Go to [https://openrouter.ai/keys](https://openrouter.ai/keys)
2. Sign up for a free account
3. Click "Create Key"
4. Copy your API key
5. **Note**: DeepSeek models are completely free to use!

#### Pexels (Required for automatic images)
1. Go to [https://www.pexels.com/api/](https://www.pexels.com/api/)
2. Sign up for a free account
3. Go to "Your API Key" section
4. Copy your API key
5. **Note**: Pexels API is free with generous limits

### Step 3: Configure the Plugin

1. In WordPress admin, go to **AI Blog Writer > Settings**
2. **API Configuration**:
   - Paste your OpenRouter API key
   - Select "DeepSeek Chat (Free)" as your model
   - Paste your Pexels API key
   - Click "Test Connection" for both APIs
3. **Content Settings**:
   - Set default word count (recommended: 800)
   - Choose whether to auto-publish posts
   - Enable auto-featured images
   - Select default category for posts
4. Click **Save Settings**

### Step 4: Generate Your First Post

1. Go to **AI Blog Writer > Generate Post**
2. Fill in the form:
   ```
   Blog Topic: "Benefits of morning workouts"
   Target Audience: "Busy professionals"
   Keywords: "morning exercise, productivity, health"
   Word Count: 800 words
   ```
3. Click **Generate Article**
4. Wait 30-60 seconds for generation
5. Review the preview
6. Click **Publish Post** or **Save as Draft**

## Detailed Configuration

### OpenRouter Model Selection

| Model | Cost | Quality | Speed | Best For |
|-------|------|---------|-------|----------|
| DeepSeek Chat | Free | High | Fast | General fitness content |
| DeepSeek R1 | Free | Very High | Medium | Complex topics |
| GPT-3.5 Turbo | Paid | High | Very Fast | Quick content |
| GPT-4 | Paid | Very High | Slow | Premium content |

**Recommendation**: Start with DeepSeek Chat (free) for excellent results.

### Content Settings Explained

- **Default Word Count**: Choose based on your content strategy
  - 300-500 words: Quick tips, social media
  - 800-1000 words: Standard blog posts
  - 1500-2000 words: In-depth guides

- **Auto Publish**: 
  - ✅ Enable if you trust the AI output
  - ❌ Disable to review content first (recommended)

- **Auto Featured Image**:
  - ✅ Enable for automatic relevant images
  - ❌ Disable if you prefer manual image selection

### Security Settings

The plugin includes built-in security features:
- Rate limiting (10 requests/hour per user)
- Input sanitization
- Nonce verification
- User capability checks

## Troubleshooting Installation

### Common Issues

#### Plugin Won't Activate
- **Check PHP version**: Requires PHP 7.4+
- **Check WordPress version**: Requires WordPress 5.0+
- **Check file permissions**: Ensure proper folder permissions

#### API Connection Fails
- **OpenRouter**: Verify API key is correct and account is active
- **Pexels**: Ensure API key is from the correct Pexels account
- **Firewall**: Check if your server blocks outgoing API requests

#### Missing Admin Menu
- **Clear cache**: Clear any caching plugins
- **Check user role**: Ensure you have administrator privileges
- **Plugin conflicts**: Deactivate other plugins temporarily

### Server Requirements

#### Minimum Requirements
- PHP 7.4+
- WordPress 5.0+
- MySQL 5.6+
- 128MB PHP memory limit
- cURL enabled
- OpenSSL enabled

#### Recommended Requirements
- PHP 8.0+
- WordPress 6.0+
- MySQL 8.0+
- 256MB PHP memory limit
- Fast internet connection

### File Permissions

Ensure proper file permissions:
```bash
# Plugin folder
chmod 755 /wp-content/plugins/ai-fitness-blog-writer/

# Plugin files
chmod 644 /wp-content/plugins/ai-fitness-blog-writer/*.php

# Assets folder
chmod 755 /wp-content/plugins/ai-fitness-blog-writer/assets/
```

## Advanced Configuration

### Custom Hooks

The plugin provides hooks for developers:

```php
// Modify generated content before saving
add_filter('afbw_before_save_content', 'my_custom_content_filter');

// Add custom validation
add_filter('afbw_validate_topic', 'my_topic_validator');

// Modify API request parameters
add_filter('afbw_openrouter_params', 'my_api_params');
```

### Database Tables

The plugin creates one custom table:
- `wp_afbw_generated_posts`: Stores generated content metadata

### Cron Jobs

The plugin sets up automatic cleanup:
- Removes old generated posts (30+ days)
- Runs daily via WordPress cron

## Migration & Backup

### Before Installation
1. **Backup your site** (database + files)
2. **Test on staging** environment first
3. **Document current setup** for rollback

### After Installation
1. **Export settings** for backup
2. **Test all functionality** thoroughly
3. **Monitor performance** impact

## Getting Help

### Self-Help Resources
1. Check this installation guide
2. Review the main README.md
3. Enable WordPress debug mode
4. Check error logs

### Support Channels
1. Plugin documentation
2. WordPress support forums
3. GitHub issues (if applicable)

### Before Contacting Support
Please provide:
- WordPress version
- PHP version
- Plugin version
- Error messages
- Steps to reproduce issue

---

**🎉 Congratulations!** You're now ready to generate amazing fitness content with AI!

Start with simple topics and gradually explore more complex content as you get familiar with the plugin.
