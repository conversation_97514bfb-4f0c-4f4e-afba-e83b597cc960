<?php
/**
 * Content generator functionality for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Generator {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Constructor is empty as this class is mainly used for organization
        // The actual generation logic is handled via AJAX in the AFBW_Ajax class
    }
    
    /**
     * Generate blog post content using OpenRouter API
     */
    public function generate_content($topic, $target_audience = '', $keywords = '', $word_count = 800) {
        $settings = get_option('afbw_settings', array());
        
        // Check if API key is configured
        if (empty($settings['openrouter_api_key'])) {
            return new WP_Error('no_api_key', __('OpenRouter API key is not configured.', 'ai-fitness-blog-writer'));
        }
        
        // Prepare the prompt
        $prompt = $this->build_prompt($topic, $target_audience, $keywords, $word_count);
        
        // Make API request
        $response = $this->make_openrouter_request($prompt, $settings);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        // Parse the response
        $content = $this->parse_openrouter_response($response);
        
        if (is_wp_error($content)) {
            return $content;
        }
        
        return array(
            'title' => $this->extract_title($content),
            'content' => $this->extract_content($content),
            'raw_response' => $content
        );
    }
    
    /**
     * Build the prompt for AI generation
     */
    private function build_prompt($topic, $target_audience, $keywords, $word_count) {
        $prompt = "You are a professional fitness and health content writer. Write a comprehensive, engaging, and informative blog post about: {$topic}\n\n";
        
        if (!empty($target_audience)) {
            $prompt .= "Target Audience: {$target_audience}\n";
        }
        
        if (!empty($keywords)) {
            $prompt .= "Keywords to include naturally: {$keywords}\n";
        }
        
        $prompt .= "Word Count: Approximately {$word_count} words\n\n";
        
        $prompt .= "Requirements:\n";
        $prompt .= "1. Start with an engaging title (use ## for the title)\n";
        $prompt .= "2. Write in a conversational, accessible tone\n";
        $prompt .= "3. Include practical tips and actionable advice\n";
        $prompt .= "4. Use proper headings (### for main sections, #### for subsections)\n";
        $prompt .= "5. Include bullet points or numbered lists where appropriate\n";
        $prompt .= "6. End with a compelling conclusion\n";
        $prompt .= "7. Ensure the content is accurate and evidence-based\n";
        $prompt .= "8. Make it SEO-friendly with natural keyword integration\n";
        $prompt .= "9. Include a brief meta description at the end (marked with 'Meta Description:')\n\n";
        
        $prompt .= "Format the response in clean Markdown format. Begin now:\n\n";
        
        return $prompt;
    }
    
    /**
     * Make request to OpenRouter API
     */
    private function make_openrouter_request($prompt, $settings) {
        $api_key = $settings['openrouter_api_key'];
        $model = $settings['openrouter_model'] ?? 'deepseek/deepseek-chat';
        
        $body = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => 4000,
            'temperature' => 0.7,
            'top_p' => 0.9
        );
        
        $response = wp_remote_post('https://openrouter.ai/api/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url(),
                'X-Title' => get_bloginfo('name') . ' - AI Fitness Blog Writer'
            ),
            'body' => json_encode($body),
            'timeout' => 60
        ));
        
        if (is_wp_error($response)) {
            return new WP_Error('api_error', __('Failed to connect to OpenRouter API: ', 'ai-fitness-blog-writer') . $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code !== 200) {
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['error']['message']) ? $error_data['error']['message'] : __('Unknown API error', 'ai-fitness-blog-writer');
            return new WP_Error('api_error', __('OpenRouter API error: ', 'ai-fitness-blog-writer') . $error_message);
        }
        
        return json_decode($body, true);
    }
    
    /**
     * Parse OpenRouter API response
     */
    private function parse_openrouter_response($response) {
        if (!isset($response['choices'][0]['message']['content'])) {
            return new WP_Error('invalid_response', __('Invalid response from OpenRouter API', 'ai-fitness-blog-writer'));
        }
        
        return $response['choices'][0]['message']['content'];
    }
    
    /**
     * Extract title from generated content
     */
    private function extract_title($content) {
        // Look for title marked with ##
        if (preg_match('/^##\s*(.+)$/m', $content, $matches)) {
            return trim($matches[1]);
        }
        
        // Fallback: look for first heading
        if (preg_match('/^#\s*(.+)$/m', $content, $matches)) {
            return trim($matches[1]);
        }
        
        // Fallback: use first line
        $lines = explode("\n", $content);
        return trim($lines[0]);
    }
    
    /**
     * Extract content from generated content (remove title)
     */
    private function extract_content($content) {
        // Remove the title line if it exists
        $content = preg_replace('/^##\s*.+$/m', '', $content, 1);
        $content = preg_replace('/^#\s*.+$/m', '', $content, 1);
        
        return trim($content);
    }
    
    /**
     * Fetch featured image from Pexels
     */
    public function fetch_featured_image($topic, $keywords = '') {
        $settings = get_option('afbw_settings', array());
        
        // Check if Pexels API key is configured
        if (empty($settings['pexels_api_key'])) {
            return new WP_Error('no_pexels_key', __('Pexels API key is not configured.', 'ai-fitness-blog-writer'));
        }
        
        // Prepare search query
        $search_terms = array();
        
        // Add topic words
        $topic_words = explode(' ', $topic);
        $search_terms = array_merge($search_terms, $topic_words);
        
        // Add keywords
        if (!empty($keywords)) {
            $keyword_array = array_map('trim', explode(',', $keywords));
            $search_terms = array_merge($search_terms, $keyword_array);
        }
        
        // Add fitness-related terms
        $search_terms[] = 'fitness';
        $search_terms[] = 'health';
        
        // Clean and prepare search query
        $search_terms = array_filter($search_terms, function($term) {
            return strlen(trim($term)) > 2;
        });
        
        $search_query = implode(' ', array_slice($search_terms, 0, 3));
        $search_query = sanitize_text_field($search_query);
        
        // Make Pexels API request
        $response = wp_remote_get(
            'https://api.pexels.com/v1/search?' . http_build_query(array(
                'query' => $search_query,
                'per_page' => 10,
                'orientation' => 'landscape'
            )),
            array(
                'headers' => array(
                    'Authorization' => $settings['pexels_api_key']
                ),
                'timeout' => 30
            )
        );
        
        if (is_wp_error($response)) {
            return new WP_Error('pexels_error', __('Failed to connect to Pexels API: ', 'ai-fitness-blog-writer') . $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code !== 200) {
            return new WP_Error('pexels_error', __('Pexels API error: ', 'ai-fitness-blog-writer') . $status_code);
        }
        
        $data = json_decode($body, true);
        
        if (empty($data['photos'])) {
            return new WP_Error('no_images', __('No suitable images found on Pexels', 'ai-fitness-blog-writer'));
        }
        
        // Select a random image from the results
        $photo = $data['photos'][array_rand($data['photos'])];
        
        return array(
            'url' => $photo['src']['large'],
            'alt' => $photo['alt'] ?? $search_query,
            'photographer' => $photo['photographer'],
            'photographer_url' => $photo['photographer_url']
        );
    }
    
    /**
     * Download and set featured image for a post
     */
    public function set_featured_image($post_id, $image_data) {
        if (is_wp_error($image_data)) {
            return $image_data;
        }
        
        // Download the image
        $image_url = $image_data['url'];
        $image_response = wp_remote_get($image_url, array('timeout' => 30));
        
        if (is_wp_error($image_response)) {
            return new WP_Error('download_error', __('Failed to download image: ', 'ai-fitness-blog-writer') . $image_response->get_error_message());
        }
        
        $image_data_binary = wp_remote_retrieve_body($image_response);
        
        // Get file info
        $filename = 'fitness-' . uniqid() . '.jpg';
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['path'] . '/' . $filename;
        $file_url = $upload_dir['url'] . '/' . $filename;
        
        // Save the file
        $file_saved = file_put_contents($file_path, $image_data_binary);
        
        if (!$file_saved) {
            return new WP_Error('save_error', __('Failed to save image file', 'ai-fitness-blog-writer'));
        }
        
        // Create attachment
        $attachment = array(
            'guid' => $file_url,
            'post_mime_type' => 'image/jpeg',
            'post_title' => $image_data['alt'],
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attachment_id = wp_insert_attachment($attachment, $file_path, $post_id);
        
        if (is_wp_error($attachment_id)) {
            return $attachment_id;
        }
        
        // Generate metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
        wp_update_attachment_metadata($attachment_id, $attachment_data);
        
        // Set as featured image
        $result = set_post_thumbnail($post_id, $attachment_id);
        
        if (!$result) {
            return new WP_Error('thumbnail_error', __('Failed to set featured image', 'ai-fitness-blog-writer'));
        }
        
        return $attachment_id;
    }
    
    /**
     * Save generated content to database
     */
    public function save_generated_content($data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'afbw_generated_posts';
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'topic' => sanitize_text_field($data['topic']),
                'target_audience' => sanitize_text_field($data['target_audience']),
                'keywords' => sanitize_textarea_field($data['keywords']),
                'word_count' => absint($data['word_count']),
                'generated_content' => wp_kses_post($data['content']),
                'featured_image_url' => esc_url_raw($data['featured_image_url'] ?? ''),
                'status' => 'generated'
            ),
            array('%s', '%s', '%s', '%d', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to save generated content to database', 'ai-fitness-blog-writer'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Create WordPress post from generated content
     */
    public function create_post($title, $content, $category_id = 1, $status = 'draft') {
        $post_data = array(
            'post_title' => sanitize_text_field($title),
            'post_content' => wp_kses_post($content),
            'post_status' => $status,
            'post_type' => 'post',
            'post_category' => array($category_id)
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            return $post_id;
        }
        
        return $post_id;
    }
}
