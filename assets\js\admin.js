/**
 * Admin JavaScript for AI Fitness Blog Writer
 */

(function($) {
    'use strict';

    // Global variables
    let currentGeneratedData = null;
    let isGenerating = false;

    $(document).ready(function() {
        initializeEventHandlers();
        initializeTooltips();
    });

    /**
     * Initialize event handlers
     */
    function initializeEventHandlers() {
        // Generate article form submission
        $('#afbw-generator-form').on('submit', handleGenerateArticle);
        
        // Preview actions
        $('#edit-content-btn').on('click', showEditForm);
        $('#publish-post-btn').on('click', publishPost);
        $('#save-draft-btn').on('click', saveDraft);
        
        // Edit form actions
        $('#back-to-preview-btn').on('click', showPreview);
        $('#afbw-edit-form').on('submit', saveEditedPost);
        
        // API connection testing
        $('#test-openrouter-btn').on('click', function() {
            testApiConnection('openrouter', $('#openrouter_api_key').val());
        });
        
        $('#test-pexels-btn').on('click', function() {
            testApiConnection('pexels', $('#pexels_api_key').val());
        });
        
        // Generated posts page actions
        $('.afbw-preview-content').on('click', previewGeneratedContent);
        $('.afbw-publish-post').on('click', publishGeneratedPost);
        $('.afbw-delete-post').on('click', deleteGeneratedPost);
        
        // Modal actions
        $('.afbw-modal-close, #afbw-close-preview').on('click', closeModal);
        
        // Close modal when clicking outside
        $(window).on('click', function(event) {
            if ($(event.target).hasClass('afbw-modal')) {
                closeModal();
            }
        });
        
        // Select all checkbox
        $('#cb-select-all-1').on('change', function() {
            $('input[name="generated_posts[]"]').prop('checked', this.checked);
        });
    }

    /**
     * Handle generate article form submission
     */
    function handleGenerateArticle(e) {
        e.preventDefault();
        
        if (isGenerating) {
            return;
        }
        
        const formData = {
            action: 'afbw_generate_content',
            nonce: afbw_ajax.nonce,
            topic: $('#blog_topic').val().trim(),
            target_audience: $('#target_audience').val().trim(),
            keywords: $('#keywords').val().trim(),
            word_count: $('#word_count').val()
        };
        
        // Validate required fields
        if (!formData.topic) {
            showStatusMessage('error', afbw_ajax.strings.error + ' Topic is required.');
            return;
        }
        
        startGeneration();
        
        $.ajax({
            url: afbw_ajax.ajax_url,
            type: 'POST',
            data: formData,
            timeout: 120000, // 2 minutes timeout
            success: function(response) {
                if (response.success) {
                    handleGenerationSuccess(response.data);
                } else {
                    handleGenerationError(response.data || 'Unknown error occurred');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Connection error occurred';
                if (status === 'timeout') {
                    errorMessage = 'Request timed out. Please try again.';
                } else if (xhr.responseJSON && xhr.responseJSON.data) {
                    errorMessage = xhr.responseJSON.data;
                }
                handleGenerationError(errorMessage);
            },
            complete: function() {
                stopGeneration();
            }
        });
    }

    /**
     * Start generation process
     */
    function startGeneration() {
        isGenerating = true;
        $('#generate-article-btn').prop('disabled', true).text(afbw_ajax.strings.generating);
        $('#afbw-spinner').addClass('is-active');
        hideContainers();
        showStatusMessage('info', 'Generating content using AI... This may take a moment.');
    }

    /**
     * Stop generation process
     */
    function stopGeneration() {
        isGenerating = false;
        $('#generate-article-btn').prop('disabled', false).text('Generate Article');
        $('#afbw-spinner').removeClass('is-active');
    }

    /**
     * Handle successful generation
     */
    function handleGenerationSuccess(data) {
        currentGeneratedData = data;
        
        // Display the generated content
        let contentHtml = '';
        if (data.title) {
            contentHtml += '<h2>' + escapeHtml(data.title) + '</h2>';
        }
        contentHtml += formatMarkdownToHtml(data.content);
        
        $('#afbw-preview-content').html(contentHtml);
        
        // Show featured image if available
        if (data.featured_image) {
            const imageHtml = `
                <div class="afbw-featured-image-preview">
                    <img src="${data.featured_image.url}" alt="${data.featured_image.alt}" />
                    <div class="afbw-featured-image-info">
                        Photo by <a href="${data.featured_image.photographer_url}" target="_blank">${data.featured_image.photographer}</a> on Pexels
                    </div>
                </div>
            `;
            $('#afbw-preview-content').prepend(imageHtml);
        }
        
        // Show word count
        if (data.word_count_actual) {
            const wordCountHtml = `<div class="afbw-word-count">Word count: ${data.word_count_actual}</div>`;
            $('#afbw-preview-content').append(wordCountHtml);
        }
        
        $('#afbw-preview-container').addClass('afbw-fade-in').show();
        showStatusMessage('success', afbw_ajax.strings.success);
        
        // Scroll to preview
        $('html, body').animate({
            scrollTop: $('#afbw-preview-container').offset().top - 50
        }, 500);
    }

    /**
     * Handle generation error
     */
    function handleGenerationError(errorMessage) {
        showStatusMessage('error', afbw_ajax.strings.error + ' ' + errorMessage);
    }

    /**
     * Show edit form
     */
    function showEditForm() {
        if (!currentGeneratedData) return;
        
        $('#post_title').val(currentGeneratedData.title || '');
        
        // Set content in WordPress editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('post_content')) {
            tinyMCE.get('post_content').setContent(formatMarkdownToHtml(currentGeneratedData.content));
        } else {
            $('#post_content').val(currentGeneratedData.content);
        }
        
        $('#afbw-preview-container').hide();
        $('#afbw-edit-container').addClass('afbw-slide-down').show();
    }

    /**
     * Show preview
     */
    function showPreview() {
        $('#afbw-edit-container').hide();
        $('#afbw-preview-container').show();
    }

    /**
     * Publish post
     */
    function publishPost() {
        if (!currentGeneratedData) return;
        
        if (!confirm(afbw_ajax.strings.confirm_publish)) {
            return;
        }
        
        const postData = {
            action: 'afbw_publish_post',
            nonce: afbw_ajax.nonce,
            title: currentGeneratedData.title,
            content: currentGeneratedData.content,
            generated_id: currentGeneratedData.generated_id,
            featured_image: currentGeneratedData.featured_image
        };
        
        $('#publish-post-btn').prop('disabled', true).text('Publishing...');
        
        $.ajax({
            url: afbw_ajax.ajax_url,
            type: 'POST',
            data: postData,
            success: function(response) {
                if (response.success) {
                    showStatusMessage('success', 'Post published successfully!');
                    
                    // Show success message with links
                    const successHtml = `
                        <div class="afbw-status-message success">
                            <p><strong>Post published successfully!</strong></p>
                            <p>
                                <a href="${response.data.edit_url}" class="button button-secondary">Edit Post</a>
                                <a href="${response.data.view_url}" class="button button-primary" target="_blank">View Post</a>
                            </p>
                        </div>
                    `;
                    $('#afbw-preview-container').after(successHtml);
                    
                    // Reset form
                    resetForm();
                } else {
                    showStatusMessage('error', response.data || 'Failed to publish post');
                }
            },
            error: function() {
                showStatusMessage('error', 'Connection error occurred while publishing');
            },
            complete: function() {
                $('#publish-post-btn').prop('disabled', false).text('Publish Post');
            }
        });
    }

    /**
     * Save as draft
     */
    function saveDraft() {
        if (!currentGeneratedData) return;
        
        const postData = {
            action: 'afbw_save_draft',
            nonce: afbw_ajax.nonce,
            title: currentGeneratedData.title,
            content: currentGeneratedData.content,
            generated_id: currentGeneratedData.generated_id,
            featured_image: currentGeneratedData.featured_image
        };
        
        $('#save-draft-btn').prop('disabled', true).text('Saving...');
        
        $.ajax({
            url: afbw_ajax.ajax_url,
            type: 'POST',
            data: postData,
            success: function(response) {
                if (response.success) {
                    showStatusMessage('success', 'Draft saved successfully!');
                    
                    // Show success message with edit link
                    const successHtml = `
                        <div class="afbw-status-message success">
                            <p><strong>Draft saved successfully!</strong></p>
                            <p>
                                <a href="${response.data.edit_url}" class="button button-primary">Edit Draft</a>
                            </p>
                        </div>
                    `;
                    $('#afbw-preview-container').after(successHtml);
                    
                    // Reset form
                    resetForm();
                } else {
                    showStatusMessage('error', response.data || 'Failed to save draft');
                }
            },
            error: function() {
                showStatusMessage('error', 'Connection error occurred while saving');
            },
            complete: function() {
                $('#save-draft-btn').prop('disabled', false).text('Save as Draft');
            }
        });
    }

    /**
     * Save edited post
     */
    function saveEditedPost(e) {
        e.preventDefault();
        
        if (!currentGeneratedData) return;
        
        // Get content from WordPress editor
        let content = '';
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('post_content')) {
            content = tinyMCE.get('post_content').getContent();
        } else {
            content = $('#post_content').val();
        }
        
        const postData = {
            action: 'afbw_publish_post',
            nonce: afbw_ajax.nonce,
            title: $('#post_title').val(),
            content: content,
            generated_id: currentGeneratedData.generated_id,
            featured_image: currentGeneratedData.featured_image
        };
        
        $('#save-edited-post-btn').prop('disabled', true).text('Saving...');
        
        $.ajax({
            url: afbw_ajax.ajax_url,
            type: 'POST',
            data: postData,
            success: function(response) {
                if (response.success) {
                    showStatusMessage('success', 'Post saved successfully!');
                    
                    // Show success message with links
                    const successHtml = `
                        <div class="afbw-status-message success">
                            <p><strong>Post saved successfully!</strong></p>
                            <p>
                                <a href="${response.data.edit_url}" class="button button-secondary">Edit Post</a>
                                <a href="${response.data.view_url}" class="button button-primary" target="_blank">View Post</a>
                            </p>
                        </div>
                    `;
                    $('#afbw-edit-container').after(successHtml);
                    
                    // Reset form
                    resetForm();
                } else {
                    showStatusMessage('error', response.data || 'Failed to save post');
                }
            },
            error: function() {
                showStatusMessage('error', 'Connection error occurred while saving');
            },
            complete: function() {
                $('#save-edited-post-btn').prop('disabled', false).text('Save Changes & Publish');
            }
        });
    }

    /**
     * Test API connection
     */
    function testApiConnection(apiType, apiKey) {
        if (!apiKey.trim()) {
            showTestResult(apiType, false, 'Please enter an API key first');
            return;
        }
        
        const button = $('#test-' + apiType + '-btn');
        const originalText = button.text();
        
        button.prop('disabled', true).text('Testing...');
        
        $.ajax({
            url: afbw_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'afbw_test_api_connection',
                nonce: afbw_ajax.nonce,
                api_type: apiType,
                api_key: apiKey
            },
            success: function(response) {
                showTestResult(apiType, response.success, response.data);
            },
            error: function() {
                showTestResult(apiType, false, 'Connection error occurred');
            },
            complete: function() {
                button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Show API test result
     */
    function showTestResult(apiType, success, message) {
        const resultDiv = $('#' + apiType + '-test-result');
        resultDiv.removeClass('success error').addClass(success ? 'success' : 'error');
        resultDiv.text(message).show();
        
        setTimeout(function() {
            resultDiv.fadeOut();
        }, 5000);
    }

    /**
     * Show status message
     */
    function showStatusMessage(type, message) {
        // Remove existing status messages
        $('.afbw-status-message').remove();
        
        const messageHtml = `<div class="afbw-status-message ${type}">${message}</div>`;
        $('.afbw-generator-container').prepend(messageHtml);
        
        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(function() {
                $('.afbw-status-message.success').fadeOut();
            }, 5000);
        }
    }

    /**
     * Hide containers
     */
    function hideContainers() {
        $('#afbw-preview-container, #afbw-edit-container').hide();
        $('.afbw-status-message').remove();
    }

    /**
     * Reset form
     */
    function resetForm() {
        $('#afbw-generator-form')[0].reset();
        hideContainers();
        currentGeneratedData = null;
    }

    /**
     * Format markdown to HTML (basic conversion)
     */
    function formatMarkdownToHtml(markdown) {
        return markdown
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^\* (.*$)/gim, '<li>$1</li>')
            .replace(/^- (.*$)/gim, '<li>$1</li>')
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            .replace(/\n\n/gim, '</p><p>')
            .replace(/^(?!<[h|l|p])/gim, '<p>')
            .replace(/(?![h|l|p]>)$/gim, '</p>')
            .replace(/<li>(.*)<\/p>/gim, '<li>$1</li>')
            .replace(/<p><\/p>/gim, '');
    }

    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Initialize tooltips
     */
    function initializeTooltips() {
        // Add tooltips to form fields if needed
        // This can be expanded based on requirements
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('.afbw-modal').hide();
    }

    /**
     * Preview generated content (for generated posts page)
     */
    function previewGeneratedContent(e) {
        e.preventDefault();
        const postId = $(this).data('id');
        
        // This would need to be implemented to fetch and display content
        // For now, just show a placeholder
        $('#afbw-preview-content-display').html('<p>Content preview functionality would be implemented here.</p>');
        $('#afbw-preview-modal').show();
    }

    /**
     * Publish generated post (for generated posts page)
     */
    function publishGeneratedPost(e) {
        e.preventDefault();
        const postId = $(this).data('id');
        
        if (!confirm('Are you sure you want to publish this post?')) {
            return;
        }
        
        // Implementation would go here
        console.log('Publishing post:', postId);
    }

    /**
     * Delete generated post (for generated posts page)
     */
    function deleteGeneratedPost(e) {
        e.preventDefault();
        const postId = $(this).data('id');
        const row = $(this).closest('tr');
        
        $.ajax({
            url: afbw_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'afbw_delete_generated_post',
                nonce: afbw_ajax.nonce,
                generated_id: postId
            },
            success: function(response) {
                if (response.success) {
                    row.fadeOut(function() {
                        row.remove();
                    });
                } else {
                    alert('Failed to delete post: ' + response.data);
                }
            },
            error: function() {
                alert('Connection error occurred while deleting post');
            }
        });
    }

})(jQuery);
