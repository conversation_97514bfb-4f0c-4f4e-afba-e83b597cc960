<?php
/**
 * Auto-posting dashboard page template for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings and status
$settings = get_option('afbw_settings', array());
require_once AFBW_PLUGIN_PATH . 'includes/class-auto-poster.php';
require_once AFBW_PLUGIN_PATH . 'includes/class-topic-pool.php';

// Get auto-posting status
$auto_posting_enabled = $settings['auto_posting_enabled'] ?? false;
$today_count = 0;
$daily_limit = $settings['auto_posting_frequency'] ?? 4;
$last_post_time = get_option('afbw_last_auto_post_time', 0);

// Get today's post count
global $wpdb;
$today_start = date('Y-m-d 00:00:00');
$today_end = date('Y-m-d 23:59:59');
$today_count = $wpdb->get_var(
    $wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}afbw_generated_posts 
        WHERE status = 'auto_published' 
        AND created_at BETWEEN %s AND %s",
        $today_start,
        $today_end
    )
);

// Get cron status
$cron_status = AFBW_Auto_Poster::get_cron_status();

// Get recent auto-posted articles
$recent_posts = $wpdb->get_results(
    "SELECT * FROM {$wpdb->prefix}afbw_generated_posts 
    WHERE status = 'auto_published' 
    ORDER BY created_at DESC 
    LIMIT 10"
);

// Get recent logs
$recent_logs = $wpdb->get_results(
    "SELECT * FROM {$wpdb->prefix}afbw_auto_posting_logs 
    ORDER BY created_at DESC 
    LIMIT 20"
);

// Get topic statistics
$topic_counts = AFBW_Topic_Pool::get_topic_count_by_category();
?>

<div class="wrap afbw-auto-posting-wrap">
    <h1><?php _e('Auto-Posting Dashboard', 'ai-fitness-blog-writer'); ?></h1>
    <p class="description"><?php _e('Monitor and manage your automatic blog post generation system.', 'ai-fitness-blog-writer'); ?></p>
    
    <!-- Status Overview -->
    <div class="afbw-dashboard-grid">
        <div class="afbw-status-card">
            <h3><?php _e('Auto-Posting Status', 'ai-fitness-blog-writer'); ?></h3>
            <div class="afbw-status-indicator <?php echo $auto_posting_enabled ? 'enabled' : 'disabled'; ?>">
                <span class="status-dot"></span>
                <?php echo $auto_posting_enabled ? __('Enabled', 'ai-fitness-blog-writer') : __('Disabled', 'ai-fitness-blog-writer'); ?>
            </div>
            <div class="afbw-quick-actions">
                <button type="button" class="button button-primary" id="toggle-auto-posting" 
                        data-enabled="<?php echo $auto_posting_enabled ? 'true' : 'false'; ?>">
                    <?php echo $auto_posting_enabled ? __('Disable Auto-Posting', 'ai-fitness-blog-writer') : __('Enable Auto-Posting', 'ai-fitness-blog-writer'); ?>
                </button>
                <button type="button" class="button button-secondary" id="force-generate-post">
                    <?php _e('Generate Post Now', 'ai-fitness-blog-writer'); ?>
                </button>
            </div>
        </div>
        
        <div class="afbw-status-card">
            <h3><?php _e('Today\'s Progress', 'ai-fitness-blog-writer'); ?></h3>
            <div class="afbw-progress-bar">
                <div class="progress-fill" style="width: <?php echo ($today_count / $daily_limit) * 100; ?>%"></div>
            </div>
            <p class="afbw-progress-text">
                <?php printf(__('%d of %d posts generated today', 'ai-fitness-blog-writer'), $today_count, $daily_limit); ?>
            </p>
            <p class="afbw-last-post">
                <?php if ($last_post_time): ?>
                    <?php printf(__('Last post: %s ago', 'ai-fitness-blog-writer'), human_time_diff($last_post_time)); ?>
                <?php else: ?>
                    <?php _e('No posts generated yet', 'ai-fitness-blog-writer'); ?>
                <?php endif; ?>
            </p>
        </div>
        
        <div class="afbw-status-card">
            <h3><?php _e('System Status', 'ai-fitness-blog-writer'); ?></h3>
            <ul class="afbw-system-status">
                <li class="<?php echo !empty($settings['openrouter_api_key']) ? 'status-ok' : 'status-error'; ?>">
                    <span class="dashicons <?php echo !empty($settings['openrouter_api_key']) ? 'dashicons-yes' : 'dashicons-no'; ?>"></span>
                    <?php _e('OpenRouter API', 'ai-fitness-blog-writer'); ?>
                </li>
                <li class="<?php echo !empty($settings['pexels_api_key']) ? 'status-ok' : 'status-error'; ?>">
                    <span class="dashicons <?php echo !empty($settings['pexels_api_key']) ? 'dashicons-yes' : 'dashicons-no'; ?>"></span>
                    <?php _e('Pexels API', 'ai-fitness-blog-writer'); ?>
                </li>
                <li class="<?php echo !$cron_status['wp_cron_disabled'] ? 'status-ok' : 'status-error'; ?>">
                    <span class="dashicons <?php echo !$cron_status['wp_cron_disabled'] ? 'dashicons-yes' : 'dashicons-no'; ?>"></span>
                    <?php _e('WordPress Cron', 'ai-fitness-blog-writer'); ?>
                </li>
                <li class="<?php echo $cron_status['auto_posting_next'] ? 'status-ok' : 'status-warning'; ?>">
                    <span class="dashicons <?php echo $cron_status['auto_posting_next'] ? 'dashicons-yes' : 'dashicons-warning'; ?>"></span>
                    <?php _e('Auto-Posting Schedule', 'ai-fitness-blog-writer'); ?>
                </li>
            </ul>
            <?php if ($cron_status['auto_posting_next']): ?>
                <p class="afbw-next-check">
                    <?php printf(__('Next check: %s', 'ai-fitness-blog-writer'), 
                        date('Y-m-d H:i:s', $cron_status['auto_posting_next'])); ?>
                </p>
            <?php endif; ?>
        </div>
        
        <div class="afbw-status-card">
            <h3><?php _e('Topic Pool', 'ai-fitness-blog-writer'); ?></h3>
            <div class="afbw-topic-stats">
                <?php foreach ($topic_counts as $category => $count): ?>
                    <div class="topic-category">
                        <span class="category-name"><?php echo esc_html(ucfirst($category)); ?></span>
                        <span class="category-count"><?php echo $count; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
            <p class="afbw-total-topics">
                <?php printf(__('Total: %d topics available', 'ai-fitness-blog-writer'), array_sum($topic_counts)); ?>
            </p>
        </div>
    </div>
    
    <!-- Recent Posts -->
    <div class="afbw-dashboard-section">
        <h2><?php _e('Recent Auto-Generated Posts', 'ai-fitness-blog-writer'); ?></h2>
        <?php if (!empty($recent_posts)): ?>
            <div class="afbw-recent-posts">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Title', 'ai-fitness-blog-writer'); ?></th>
                            <th><?php _e('Topic', 'ai-fitness-blog-writer'); ?></th>
                            <th><?php _e('Word Count', 'ai-fitness-blog-writer'); ?></th>
                            <th><?php _e('Generated', 'ai-fitness-blog-writer'); ?></th>
                            <th><?php _e('Actions', 'ai-fitness-blog-writer'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_posts as $post): ?>
                            <tr>
                                <td>
                                    <?php if ($post->post_id): ?>
                                        <a href="<?php echo get_edit_post_link($post->post_id); ?>" target="_blank">
                                            <?php echo esc_html(get_the_title($post->post_id) ?: $post->topic); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo esc_html($post->topic); ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($post->topic); ?></td>
                                <td><?php echo esc_html($post->word_count); ?></td>
                                <td><?php echo esc_html(date('M j, Y H:i', strtotime($post->created_at))); ?></td>
                                <td>
                                    <?php if ($post->post_id): ?>
                                        <a href="<?php echo get_permalink($post->post_id); ?>" target="_blank" class="button button-small">
                                            <?php _e('View', 'ai-fitness-blog-writer'); ?>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p><?php _e('No auto-generated posts yet.', 'ai-fitness-blog-writer'); ?></p>
        <?php endif; ?>
    </div>
    
    <!-- Activity Log -->
    <div class="afbw-dashboard-section">
        <h2><?php _e('Activity Log', 'ai-fitness-blog-writer'); ?></h2>
        <?php if (!empty($recent_logs)): ?>
            <div class="afbw-activity-log">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Type', 'ai-fitness-blog-writer'); ?></th>
                            <th><?php _e('Message', 'ai-fitness-blog-writer'); ?></th>
                            <th><?php _e('Time', 'ai-fitness-blog-writer'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_logs as $log): ?>
                            <tr>
                                <td>
                                    <span class="log-type log-type-<?php echo esc_attr($log->log_type); ?>">
                                        <?php echo esc_html(ucfirst($log->log_type)); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html($log->message); ?></td>
                                <td><?php echo esc_html(date('M j, Y H:i:s', strtotime($log->created_at))); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p><?php _e('No activity logged yet.', 'ai-fitness-blog-writer'); ?></p>
        <?php endif; ?>
    </div>
    
    <!-- Settings Quick Access -->
    <div class="afbw-dashboard-section">
        <h2><?php _e('Quick Settings', 'ai-fitness-blog-writer'); ?></h2>
        <div class="afbw-quick-settings">
            <div class="setting-item">
                <label><?php _e('Posts per day:', 'ai-fitness-blog-writer'); ?></label>
                <span><?php echo esc_html($settings['auto_posting_frequency'] ?? 4); ?></span>
            </div>
            <div class="setting-item">
                <label><?php _e('Posting hours:', 'ai-fitness-blog-writer'); ?></label>
                <span><?php echo esc_html(($settings['auto_posting_start_time'] ?? '08:00') . ' - ' . ($settings['auto_posting_end_time'] ?? '18:00')); ?></span>
            </div>
            <div class="setting-item">
                <label><?php _e('Minimum interval:', 'ai-fitness-blog-writer'); ?></label>
                <span><?php printf(__('%d hours', 'ai-fitness-blog-writer'), $settings['auto_posting_min_interval'] ?? 2); ?></span>
            </div>
            <div class="setting-item">
                <label><?php _e('Word count range:', 'ai-fitness-blog-writer'); ?></label>
                <span><?php printf(__('%d - %d words', 'ai-fitness-blog-writer'), 
                    $settings['auto_posting_word_count_min'] ?? 600, 
                    $settings['auto_posting_word_count_max'] ?? 1200); ?></span>
            </div>
        </div>
        <p>
            <a href="<?php echo admin_url('admin.php?page=afbw-settings'); ?>" class="button button-secondary">
                <?php _e('Modify Settings', 'ai-fitness-blog-writer'); ?>
            </a>
        </p>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Toggle auto-posting
    $('#toggle-auto-posting').on('click', function() {
        var button = $(this);
        var enabled = button.data('enabled') === 'true';
        
        button.prop('disabled', true).text('<?php _e('Processing...', 'ai-fitness-blog-writer'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'afbw_toggle_auto_posting',
                nonce: '<?php echo wp_create_nonce('afbw_nonce'); ?>',
                enabled: !enabled
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data || '<?php _e('Error occurred', 'ai-fitness-blog-writer'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Connection error', 'ai-fitness-blog-writer'); ?>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });
    
    // Force generate post
    $('#force-generate-post').on('click', function() {
        var button = $(this);
        
        if (!confirm('<?php _e('Generate a new post now?', 'ai-fitness-blog-writer'); ?>')) {
            return;
        }
        
        button.prop('disabled', true).text('<?php _e('Generating...', 'ai-fitness-blog-writer'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'afbw_force_generate_post',
                nonce: '<?php echo wp_create_nonce('afbw_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Post generated successfully!', 'ai-fitness-blog-writer'); ?>');
                    location.reload();
                } else {
                    alert(response.data || '<?php _e('Failed to generate post', 'ai-fitness-blog-writer'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Connection error', 'ai-fitness-blog-writer'); ?>');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Generate Post Now', 'ai-fitness-blog-writer'); ?>');
            }
        });
    });
});
</script>

<style>
.afbw-auto-posting-wrap {
    max-width: 1200px;
}

.afbw-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.afbw-status-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.afbw-status-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    color: #666;
}

.afbw-status-indicator {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.afbw-status-indicator.enabled {
    color: #46b450;
}

.afbw-status-indicator.disabled {
    color: #dc3232;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.afbw-status-indicator.enabled .status-dot {
    background-color: #46b450;
}

.afbw-status-indicator.disabled .status-dot {
    background-color: #dc3232;
}

.afbw-quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.afbw-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f1f1f1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: #0073aa;
    transition: width 0.3s ease;
}

.afbw-progress-text {
    font-weight: 600;
    margin-bottom: 5px;
}

.afbw-last-post {
    color: #666;
    font-size: 13px;
    margin: 0;
}

.afbw-system-status {
    list-style: none;
    margin: 0;
    padding: 0;
}

.afbw-system-status li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.afbw-system-status .dashicons {
    margin-right: 8px;
    font-size: 16px;
}

.status-ok {
    color: #46b450;
}

.status-error {
    color: #dc3232;
}

.status-warning {
    color: #ffb900;
}

.afbw-next-check {
    font-size: 12px;
    color: #666;
    margin-top: 10px;
}

.afbw-topic-stats {
    margin-bottom: 10px;
}

.topic-category {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 14px;
}

.category-count {
    font-weight: 600;
    color: #0073aa;
}

.afbw-total-topics {
    font-weight: 600;
    color: #666;
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin: 10px 0 0 0;
}

.afbw-dashboard-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.afbw-dashboard-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.log-type {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.log-type-success {
    background-color: #d4edda;
    color: #155724;
}

.log-type-error {
    background-color: #f8d7da;
    color: #721c24;
}

.log-type-warning {
    background-color: #fff3cd;
    color: #856404;
}

.afbw-quick-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.setting-item label {
    font-weight: 600;
    margin: 0;
}

.setting-item span {
    color: #0073aa;
    font-weight: 600;
}
</style>
