<?php
/**
 * Topic Pool Management for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Topic_Pool {
    
    /**
     * Get all available fitness topics
     */
    public static function get_all_topics() {
        $topics = array(
            // Strength Training Topics
            'strength' => array(
                array(
                    'topic' => 'Progressive Overload: The Key to Muscle Growth',
                    'audience' => 'intermediate lifters',
                    'keywords' => 'progressive overload, muscle growth, strength training, weightlifting',
                    'category' => 'strength'
                ),
                array(
                    'topic' => 'Compound vs Isolation Exercises: Which is Better?',
                    'audience' => 'fitness enthusiasts',
                    'keywords' => 'compound exercises, isolation exercises, strength training, muscle building',
                    'category' => 'strength'
                ),
                array(
                    'topic' => 'Deadlift Technique: Master the King of Exercises',
                    'audience' => 'strength training beginners',
                    'keywords' => 'deadlift, technique, form, strength training, powerlifting',
                    'category' => 'strength'
                ),
                array(
                    'topic' => 'Building a Home Gym on a Budget',
                    'audience' => 'budget-conscious fitness enthusiasts',
                    'keywords' => 'home gym, budget fitness, equipment, strength training',
                    'category' => 'strength'
                ),
                array(
                    'topic' => 'The Science of Muscle Hypertrophy',
                    'audience' => 'advanced lifters',
                    'keywords' => 'muscle hypertrophy, muscle growth, science, bodybuilding',
                    'category' => 'strength'
                )
            ),
            
            // Cardio Topics
            'cardio' => array(
                array(
                    'topic' => 'HIIT vs LISS: Which Cardio is Right for You?',
                    'audience' => 'fitness enthusiasts',
                    'keywords' => 'HIIT, LISS, cardio, fat loss, endurance',
                    'category' => 'cardio'
                ),
                array(
                    'topic' => 'Running for Beginners: Complete Guide',
                    'audience' => 'running beginners',
                    'keywords' => 'running, beginners guide, cardio, endurance, fitness',
                    'category' => 'cardio'
                ),
                array(
                    'topic' => 'Tabata Training: 4-Minute Fat Burning Workout',
                    'audience' => 'busy professionals',
                    'keywords' => 'Tabata, HIIT, fat burning, quick workout, time-efficient',
                    'category' => 'cardio'
                ),
                array(
                    'topic' => 'Swimming for Total Body Fitness',
                    'audience' => 'fitness enthusiasts',
                    'keywords' => 'swimming, total body workout, cardio, low impact exercise',
                    'category' => 'cardio'
                ),
                array(
                    'topic' => 'Cycling: Indoor vs Outdoor Training Benefits',
                    'audience' => 'cycling enthusiasts',
                    'keywords' => 'cycling, indoor training, outdoor cycling, cardio, endurance',
                    'category' => 'cardio'
                )
            ),
            
            // Nutrition Topics
            'nutrition' => array(
                array(
                    'topic' => 'Macronutrients Explained: Protein, Carbs, and Fats',
                    'audience' => 'nutrition beginners',
                    'keywords' => 'macronutrients, protein, carbohydrates, fats, nutrition basics',
                    'category' => 'nutrition'
                ),
                array(
                    'topic' => 'Pre and Post Workout Nutrition Guide',
                    'audience' => 'athletes and fitness enthusiasts',
                    'keywords' => 'pre-workout nutrition, post-workout nutrition, performance, recovery',
                    'category' => 'nutrition'
                ),
                array(
                    'topic' => 'Intermittent Fasting for Weight Loss',
                    'audience' => 'weight loss seekers',
                    'keywords' => 'intermittent fasting, weight loss, fat loss, diet strategy',
                    'category' => 'nutrition'
                ),
                array(
                    'topic' => 'Plant-Based Protein Sources for Athletes',
                    'audience' => 'vegan athletes',
                    'keywords' => 'plant-based protein, vegan nutrition, athletic performance, protein sources',
                    'category' => 'nutrition'
                ),
                array(
                    'topic' => 'Hydration: The Forgotten Key to Performance',
                    'audience' => 'athletes',
                    'keywords' => 'hydration, performance, electrolytes, water intake, athletic performance',
                    'category' => 'nutrition'
                )
            ),
            
            // Weight Loss Topics
            'weight-loss' => array(
                array(
                    'topic' => 'Creating a Sustainable Calorie Deficit',
                    'audience' => 'weight loss beginners',
                    'keywords' => 'calorie deficit, weight loss, sustainable diet, fat loss',
                    'category' => 'weight-loss'
                ),
                array(
                    'topic' => 'Metabolism Myths: What Really Affects Your Metabolic Rate',
                    'audience' => 'weight loss seekers',
                    'keywords' => 'metabolism, metabolic rate, weight loss myths, fat burning',
                    'category' => 'weight-loss'
                ),
                array(
                    'topic' => 'Strength Training for Fat Loss',
                    'audience' => 'weight loss enthusiasts',
                    'keywords' => 'strength training, fat loss, weight loss, muscle preservation',
                    'category' => 'weight-loss'
                ),
                array(
                    'topic' => 'Breaking Weight Loss Plateaus',
                    'audience' => 'experienced dieters',
                    'keywords' => 'weight loss plateau, fat loss plateau, diet breaks, metabolism',
                    'category' => 'weight-loss'
                ),
                array(
                    'topic' => 'The Psychology of Weight Loss Success',
                    'audience' => 'weight loss strugglers',
                    'keywords' => 'weight loss psychology, motivation, habits, mindset, success',
                    'category' => 'weight-loss'
                )
            ),
            
            // Yoga and Flexibility Topics
            'yoga' => array(
                array(
                    'topic' => 'Yoga for Athletes: Improving Performance and Recovery',
                    'audience' => 'athletes',
                    'keywords' => 'yoga for athletes, flexibility, recovery, performance, mobility',
                    'category' => 'yoga'
                ),
                array(
                    'topic' => 'Morning Yoga Routine for Energy and Focus',
                    'audience' => 'busy professionals',
                    'keywords' => 'morning yoga, energy, focus, daily routine, mindfulness',
                    'category' => 'yoga'
                ),
                array(
                    'topic' => 'Restorative Yoga for Stress Relief',
                    'audience' => 'stressed individuals',
                    'keywords' => 'restorative yoga, stress relief, relaxation, mental health, wellness',
                    'category' => 'yoga'
                ),
                array(
                    'topic' => 'Dynamic Stretching vs Static Stretching',
                    'audience' => 'fitness enthusiasts',
                    'keywords' => 'dynamic stretching, static stretching, flexibility, mobility, warm-up',
                    'category' => 'yoga'
                ),
                array(
                    'topic' => 'Yoga Poses for Better Posture',
                    'audience' => 'office workers',
                    'keywords' => 'yoga poses, posture, spinal health, desk workers, alignment',
                    'category' => 'yoga'
                )
            ),
            
            // Mental Health and Wellness Topics
            'wellness' => array(
                array(
                    'topic' => 'Exercise and Mental Health: The Science Connection',
                    'audience' => 'mental health advocates',
                    'keywords' => 'exercise, mental health, depression, anxiety, endorphins',
                    'category' => 'wellness'
                ),
                array(
                    'topic' => 'Sleep Optimization for Athletic Performance',
                    'audience' => 'athletes',
                    'keywords' => 'sleep, recovery, performance, athletic performance, rest',
                    'category' => 'wellness'
                ),
                array(
                    'topic' => 'Stress Management Through Physical Activity',
                    'audience' => 'stressed professionals',
                    'keywords' => 'stress management, physical activity, exercise, mental health, wellness',
                    'category' => 'wellness'
                ),
                array(
                    'topic' => 'Building Healthy Habits That Stick',
                    'audience' => 'habit builders',
                    'keywords' => 'healthy habits, habit formation, lifestyle change, consistency, motivation',
                    'category' => 'wellness'
                ),
                array(
                    'topic' => 'Mindful Eating for Better Health',
                    'audience' => 'health-conscious individuals',
                    'keywords' => 'mindful eating, nutrition, health, wellness, eating habits',
                    'category' => 'wellness'
                )
            ),
            
            // Functional Fitness Topics
            'functional' => array(
                array(
                    'topic' => 'Functional Movement Patterns for Daily Life',
                    'audience' => 'general fitness enthusiasts',
                    'keywords' => 'functional movement, daily activities, mobility, strength, movement patterns',
                    'category' => 'functional'
                ),
                array(
                    'topic' => 'Core Strength Beyond Crunches',
                    'audience' => 'fitness enthusiasts',
                    'keywords' => 'core strength, functional core, stability, abdominal training, core exercises',
                    'category' => 'functional'
                ),
                array(
                    'topic' => 'Balance Training for Injury Prevention',
                    'audience' => 'injury prevention seekers',
                    'keywords' => 'balance training, injury prevention, stability, proprioception, functional fitness',
                    'category' => 'functional'
                ),
                array(
                    'topic' => 'Mobility Work for Better Movement Quality',
                    'audience' => 'movement quality enthusiasts',
                    'keywords' => 'mobility, movement quality, flexibility, joint health, functional movement',
                    'category' => 'functional'
                ),
                array(
                    'topic' => 'Bodyweight Training: No Gym Required',
                    'audience' => 'home fitness enthusiasts',
                    'keywords' => 'bodyweight training, home workout, calisthenics, functional fitness, no equipment',
                    'category' => 'functional'
                )
            )
        );
        
        return apply_filters('afbw_topic_pool', $topics);
    }
    
    /**
     * Get random topic from all categories
     */
    public static function get_random_topic() {
        $all_topics = self::get_all_topics();
        $flat_topics = array();
        
        // Flatten the topics array
        foreach ($all_topics as $category => $topics) {
            $flat_topics = array_merge($flat_topics, $topics);
        }
        
        if (empty($flat_topics)) {
            return false;
        }
        
        $random_key = array_rand($flat_topics);
        return $flat_topics[$random_key];
    }
    
    /**
     * Get random topic from specific category
     */
    public static function get_random_topic_by_category($category) {
        $all_topics = self::get_all_topics();
        
        if (!isset($all_topics[$category]) || empty($all_topics[$category])) {
            return false;
        }
        
        $category_topics = $all_topics[$category];
        $random_key = array_rand($category_topics);
        
        return $category_topics[$random_key];
    }
    
    /**
     * Get topics by multiple categories
     */
    public static function get_topics_by_categories($categories) {
        $all_topics = self::get_all_topics();
        $filtered_topics = array();
        
        foreach ($categories as $category) {
            if (isset($all_topics[$category])) {
                $filtered_topics = array_merge($filtered_topics, $all_topics[$category]);
            }
        }
        
        return $filtered_topics;
    }
    
    /**
     * Get available categories
     */
    public static function get_categories() {
        return array(
            'strength' => __('Strength Training', 'ai-fitness-blog-writer'),
            'cardio' => __('Cardio & Endurance', 'ai-fitness-blog-writer'),
            'nutrition' => __('Nutrition', 'ai-fitness-blog-writer'),
            'weight-loss' => __('Weight Loss', 'ai-fitness-blog-writer'),
            'yoga' => __('Yoga & Flexibility', 'ai-fitness-blog-writer'),
            'wellness' => __('Mental Health & Wellness', 'ai-fitness-blog-writer'),
            'functional' => __('Functional Fitness', 'ai-fitness-blog-writer')
        );
    }
    
    /**
     * Get topic count by category
     */
    public static function get_topic_count_by_category() {
        $all_topics = self::get_all_topics();
        $counts = array();
        
        foreach ($all_topics as $category => $topics) {
            $counts[$category] = count($topics);
        }
        
        return $counts;
    }
    
    /**
     * Add custom topic (for future expansion)
     */
    public static function add_custom_topic($topic_data) {
        $custom_topics = get_option('afbw_custom_topics', array());
        $custom_topics[] = $topic_data;
        update_option('afbw_custom_topics', $custom_topics);
    }
    
    /**
     * Get custom topics
     */
    public static function get_custom_topics() {
        return get_option('afbw_custom_topics', array());
    }
    
    /**
     * Get weighted random topic (considers usage history)
     */
    public static function get_weighted_random_topic() {
        $all_topics = self::get_all_topics();
        $flat_topics = array();
        
        // Flatten topics and add weights
        foreach ($all_topics as $category => $topics) {
            foreach ($topics as $topic) {
                $topic['weight'] = self::calculate_topic_weight($topic);
                $flat_topics[] = $topic;
            }
        }
        
        // Sort by weight (higher weight = less recently used)
        usort($flat_topics, function($a, $b) {
            return $b['weight'] <=> $a['weight'];
        });
        
        // Use weighted random selection from top 50% of topics
        $top_topics = array_slice($flat_topics, 0, ceil(count($flat_topics) / 2));
        
        if (empty($top_topics)) {
            return false;
        }
        
        $random_key = array_rand($top_topics);
        return $top_topics[$random_key];
    }
    
    /**
     * Calculate topic weight based on usage history
     */
    private static function calculate_topic_weight($topic) {
        global $wpdb;
        
        // Get usage count in last 30 days
        $usage_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}afbw_generated_posts 
                WHERE topic = %s 
                AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)",
                $topic['topic']
            )
        );
        
        // Higher weight for less used topics
        return max(1, 10 - intval($usage_count));
    }
}
