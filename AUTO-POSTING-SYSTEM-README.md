# AI Fitness Blog Writer - Auto-Posting System

## 🚀 Overview

The AI Fitness Blog Writer now includes a comprehensive auto-posting system that can automatically generate and publish 3-5 high-quality fitness blog posts per day. This system uses advanced AI content generation, intelligent topic selection, and robust safety features to ensure consistent, engaging content for your fitness blog.

## ✨ Key Features

### 🤖 Intelligent Auto-Posting
- **Configurable Frequency**: Generate 1-8 posts per day
- **Smart Timing**: Set custom posting hours (e.g., 8 AM - 6 PM)
- **Randomized Scheduling**: Natural posting patterns to avoid detection
- **Minimum Intervals**: Prevent spam with configurable time gaps between posts

### 📚 Advanced Topic Pool System
- **200+ Pre-defined Topics**: Covering 7 major fitness categories
- **Weighted Selection**: Ensures topic variety and prevents repetition
- **Category-based Filtering**: Focus on specific fitness areas
- **Usage Tracking**: Automatically avoids recently used topics

### 🛡️ Safety & Reliability Features
- **API Rate Limiting**: Prevents quota exhaustion
- **Error Handling**: Automatic retry with fallback topics
- **Daily Quotas**: Safety limits to prevent over-posting
- **Comprehensive Logging**: Track all activities and errors
- **Health Monitoring**: Real-time system status checks

### 📊 Management Dashboard
- **Live Status Monitoring**: See auto-posting status at a glance
- **Progress Tracking**: Daily post count and progress bars
- **System Health**: API status, cron jobs, and database checks
- **Activity Logs**: Detailed history of all auto-posting activities
- **Manual Controls**: Force generate posts or toggle auto-posting

## 🏗️ System Architecture

### Core Components

1. **AFBW_Auto_Poster** (`includes/class-auto-poster.php`)
   - Main auto-posting logic
   - Cron job handlers
   - Safety checks and rate limiting
   - Error handling and retry logic

2. **AFBW_Topic_Pool** (`includes/class-topic-pool.php`)
   - Topic management system
   - Weighted random selection
   - Category-based filtering
   - Usage tracking

3. **Auto-Posting Dashboard** (`includes/admin-auto-posting.php`)
   - Real-time monitoring interface
   - System status overview
   - Manual controls
   - Activity logs

4. **Enhanced Settings** (`includes/admin-settings.php`)
   - Auto-posting configuration
   - Timing and frequency settings
   - Topic category selection
   - Word count ranges

### Database Tables

1. **`wp_afbw_generated_posts`** (Enhanced)
   - Stores all generated content
   - Tracks auto-published posts
   - Links to WordPress posts

2. **`wp_afbw_auto_posting_logs`** (New)
   - Comprehensive activity logging
   - Error tracking
   - Success metrics

### WordPress Cron Jobs

1. **`afbw_auto_posting_check`** (Hourly)
   - Checks if new post should be generated
   - Validates posting hours and intervals
   - Triggers post generation

2. **`afbw_daily_cleanup`** (Daily)
   - Cleans up old logs
   - Resets daily counters
   - Maintenance tasks

## 🎯 Topic Categories

The system includes 200+ pre-defined topics across 7 categories:

1. **Strength Training** (25 topics)
   - Progressive overload, compound exercises, deadlifts, home gyms, etc.

2. **Cardio & Endurance** (25 topics)
   - HIIT vs LISS, running, Tabata, swimming, cycling, etc.

3. **Nutrition** (25 topics)
   - Macronutrients, pre/post workout nutrition, intermittent fasting, etc.

4. **Weight Loss** (25 topics)
   - Calorie deficits, metabolism, fat loss strategies, etc.

5. **Yoga & Flexibility** (25 topics)
   - Yoga for athletes, morning routines, stretching, posture, etc.

6. **Mental Health & Wellness** (25 topics)
   - Exercise and mental health, sleep optimization, stress management, etc.

7. **Functional Fitness** (25 topics)
   - Movement patterns, core strength, balance training, bodyweight exercises, etc.

## ⚙️ Configuration Options

### Auto-Posting Settings

- **Enable/Disable**: Toggle auto-posting on/off
- **Posts Per Day**: 1-8 posts (recommended: 3-5)
- **Start Time**: When to begin posting each day
- **End Time**: When to stop posting each day
- **Minimum Interval**: Hours between posts (recommended: 2-4)
- **Randomize Timing**: Add natural variation to posting times
- **Topic Categories**: Select which categories to use
- **Word Count Range**: Min/max words per post (600-1200 recommended)

### Safety Limits

- **API Rate Limiting**: 10 calls per hour maximum
- **Daily Quota**: 150% of configured posts per day
- **Retry Logic**: Up to 3 attempts with different topics
- **Error Handling**: Graceful failure with detailed logging

## 🚀 Getting Started

### 1. Initial Setup

1. **Configure API Keys**:
   - Go to **AI Blog Writer > Settings**
   - Add your OpenRouter API key
   - Add your Pexels API key (optional)

2. **Configure Auto-Posting**:
   - Scroll to "Auto-Posting Settings"
   - Set your desired frequency (3-5 posts recommended)
   - Set posting hours (e.g., 8 AM - 6 PM)
   - Choose topic categories
   - Set word count range

3. **Enable Auto-Posting**:
   - Check "Enable Auto-Posting"
   - Save settings

### 2. Monitor and Manage

1. **Dashboard Access**:
   - Go to **AI Blog Writer > Auto-Posting**
   - View real-time status and progress

2. **Manual Controls**:
   - Force generate posts for testing
   - Toggle auto-posting on/off
   - View activity logs

### 3. Testing

1. **Run System Test**:
   - Access: `your-site.com/wp-admin/admin.php?page=afbw-auto-posting&afbw_test=1`
   - Check all system components
   - Verify configuration

2. **Generate Test Post**:
   - Use "Generate Post Now" button
   - Verify content quality
   - Check featured images

## 📈 Monitoring & Analytics

### Dashboard Metrics

- **Today's Progress**: Posts generated vs daily limit
- **System Status**: API health, cron jobs, database
- **Topic Pool**: Available topics by category
- **Recent Posts**: Latest auto-generated content
- **Activity Logs**: Success/error tracking

### Performance Tracking

- **API Usage Stats**: Track OpenRouter and Pexels calls
- **Success Rates**: Monitor generation success/failure
- **Topic Distribution**: Ensure variety across categories
- **Timing Analysis**: Verify posting schedule adherence

## 🛠️ Troubleshooting

### Common Issues

1. **No Posts Generated**:
   - Check API keys are valid
   - Verify auto-posting is enabled
   - Check posting hours
   - Review activity logs

2. **Cron Jobs Not Running**:
   - Verify WordPress cron is enabled
   - Check server cron configuration
   - Use WP-CLI to test: `wp cron event list`

3. **API Errors**:
   - Check API key validity
   - Monitor rate limits
   - Review error logs

### Debug Mode

Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 🔧 Advanced Customization

### Custom Topics

Add your own topics using the filter:
```php
add_filter('afbw_topic_pool', function($topics) {
    $topics['custom'] = array(
        array(
            'topic' => 'Your Custom Topic',
            'audience' => 'Target Audience',
            'keywords' => 'keyword1, keyword2',
            'category' => 'custom'
        )
    );
    return $topics;
});
```

### Custom Scheduling

Modify posting logic:
```php
add_filter('afbw_should_generate_post', function($should_generate, $settings) {
    // Your custom logic here
    return $should_generate;
}, 10, 2);
```

### API Rate Limits

Adjust rate limits:
```php
add_filter('afbw_hourly_api_limit', function($limit) {
    return 20; // Increase to 20 calls per hour
});
```

## 📞 Support

For issues or questions:

1. Check the **Activity Logs** in the dashboard
2. Run the **System Test** to identify problems
3. Review WordPress error logs
4. Check API key validity and quotas

## 🎉 Success Tips

1. **Start Small**: Begin with 2-3 posts per day
2. **Monitor Quality**: Review generated content regularly
3. **Adjust Topics**: Customize categories for your audience
4. **Check Analytics**: Monitor engagement on auto-posted content
5. **Stay Updated**: Keep API keys active and monitor quotas

The auto-posting system is designed to run reliably with minimal intervention while producing high-quality, engaging fitness content for your blog!
