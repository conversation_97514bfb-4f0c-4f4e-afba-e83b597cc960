<?php
/**
 * Settings functionality for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Settings {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_afbw_test_api_connection', array($this, 'test_api_connection'));
    }
    
    /**
     * Register plugin settings
     */
    public function register_settings() {
        register_setting(
            'afbw_settings_group',
            'afbw_settings',
            array($this, 'sanitize_settings')
        );
        
        // API Settings Section
        add_settings_section(
            'afbw_api_settings',
            __('API Configuration', 'ai-fitness-blog-writer'),
            array($this, 'api_settings_section_callback'),
            'afbw_settings'
        );
        
        // OpenRouter API Key
        add_settings_field(
            'openrouter_api_key',
            __('OpenRouter API Key', 'ai-fitness-blog-writer'),
            array($this, 'openrouter_api_key_callback'),
            'afbw_settings',
            'afbw_api_settings'
        );
        
        // OpenRouter Model
        add_settings_field(
            'openrouter_model',
            __('OpenRouter Model', 'ai-fitness-blog-writer'),
            array($this, 'openrouter_model_callback'),
            'afbw_settings',
            'afbw_api_settings'
        );
        
        // Pexels API Key
        add_settings_field(
            'pexels_api_key',
            __('Pexels API Key', 'ai-fitness-blog-writer'),
            array($this, 'pexels_api_key_callback'),
            'afbw_settings',
            'afbw_api_settings'
        );
        
        // Content Settings Section
        add_settings_section(
            'afbw_content_settings',
            __('Content Settings', 'ai-fitness-blog-writer'),
            array($this, 'content_settings_section_callback'),
            'afbw_settings'
        );
        
        // Default Word Count
        add_settings_field(
            'default_word_count',
            __('Default Word Count', 'ai-fitness-blog-writer'),
            array($this, 'default_word_count_callback'),
            'afbw_settings',
            'afbw_content_settings'
        );
        
        // Auto Publish
        add_settings_field(
            'auto_publish',
            __('Auto Publish Posts', 'ai-fitness-blog-writer'),
            array($this, 'auto_publish_callback'),
            'afbw_settings',
            'afbw_content_settings'
        );
        
        // Auto Set Featured Image
        add_settings_field(
            'auto_set_featured_image',
            __('Auto Set Featured Image', 'ai-fitness-blog-writer'),
            array($this, 'auto_set_featured_image_callback'),
            'afbw_settings',
            'afbw_content_settings'
        );
        
        // Default Post Status
        add_settings_field(
            'post_status',
            __('Default Post Status', 'ai-fitness-blog-writer'),
            array($this, 'post_status_callback'),
            'afbw_settings',
            'afbw_content_settings'
        );
        
        // Default Post Category
        add_settings_field(
            'post_category',
            __('Default Post Category', 'ai-fitness-blog-writer'),
            array($this, 'post_category_callback'),
            'afbw_settings',
            'afbw_content_settings'
        );
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        // Sanitize API keys
        $sanitized['openrouter_api_key'] = sanitize_text_field($input['openrouter_api_key']);
        $sanitized['pexels_api_key'] = sanitize_text_field($input['pexels_api_key']);
        
        // Sanitize model name
        $sanitized['openrouter_model'] = sanitize_text_field($input['openrouter_model']);
        
        // Sanitize numeric values
        $sanitized['default_word_count'] = absint($input['default_word_count']);
        $sanitized['post_category'] = absint($input['post_category']);
        
        // Sanitize boolean values
        $sanitized['auto_publish'] = isset($input['auto_publish']) ? true : false;
        $sanitized['auto_set_featured_image'] = isset($input['auto_set_featured_image']) ? true : false;
        
        // Sanitize post status
        $allowed_statuses = array('draft', 'publish', 'private');
        $sanitized['post_status'] = in_array($input['post_status'], $allowed_statuses) ? $input['post_status'] : 'draft';
        
        return $sanitized;
    }
    
    /**
     * API settings section callback
     */
    public function api_settings_section_callback() {
        echo '<p>' . __('Configure your API keys for OpenRouter.ai and Pexels to enable content generation and image fetching.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Content settings section callback
     */
    public function content_settings_section_callback() {
        echo '<p>' . __('Configure default settings for generated blog posts.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * OpenRouter API Key field callback
     */
    public function openrouter_api_key_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['openrouter_api_key']) ? $settings['openrouter_api_key'] : '';
        
        echo '<input type="password" id="openrouter_api_key" name="afbw_settings[openrouter_api_key]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" class="button button-secondary" id="test-openrouter-btn" style="margin-left: 10px;">' . __('Test Connection', 'ai-fitness-blog-writer') . '</button>';
        echo '<p class="description">' . sprintf(
            __('Get your API key from <a href="%s" target="_blank">OpenRouter.ai</a>', 'ai-fitness-blog-writer'),
            'https://openrouter.ai/keys'
        ) . '</p>';
        echo '<div id="openrouter-test-result" class="afbw-test-result"></div>';
    }
    
    /**
     * OpenRouter Model field callback
     */
    public function openrouter_model_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['openrouter_model']) ? $settings['openrouter_model'] : 'deepseek/deepseek-chat';
        
        $models = array(
            'deepseek/deepseek-chat' => 'DeepSeek Chat (Free)',
            'deepseek/deepseek-r1' => 'DeepSeek R1 (Free)',
            'openai/gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'openai/gpt-4' => 'GPT-4',
            'anthropic/claude-3-haiku' => 'Claude 3 Haiku',
            'meta-llama/llama-3.1-8b-instruct' => 'Llama 3.1 8B'
        );
        
        echo '<select id="openrouter_model" name="afbw_settings[openrouter_model]" class="regular-text">';
        foreach ($models as $model_id => $model_name) {
            echo '<option value="' . esc_attr($model_id) . '"' . selected($value, $model_id, false) . '>' . esc_html($model_name) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Select the AI model to use for content generation. DeepSeek models are free.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Pexels API Key field callback
     */
    public function pexels_api_key_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['pexels_api_key']) ? $settings['pexels_api_key'] : '';
        
        echo '<input type="password" id="pexels_api_key" name="afbw_settings[pexels_api_key]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" class="button button-secondary" id="test-pexels-btn" style="margin-left: 10px;">' . __('Test Connection', 'ai-fitness-blog-writer') . '</button>';
        echo '<p class="description">' . sprintf(
            __('Get your API key from <a href="%s" target="_blank">Pexels</a>', 'ai-fitness-blog-writer'),
            'https://www.pexels.com/api/'
        ) . '</p>';
        echo '<div id="pexels-test-result" class="afbw-test-result"></div>';
    }
    
    /**
     * Default Word Count field callback
     */
    public function default_word_count_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['default_word_count']) ? $settings['default_word_count'] : 800;
        
        echo '<select id="default_word_count" name="afbw_settings[default_word_count]">';
        $word_counts = array(300, 500, 800, 1000, 1500, 2000);
        foreach ($word_counts as $count) {
            echo '<option value="' . $count . '"' . selected($value, $count, false) . '>' . $count . ' words</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Default word count for generated blog posts.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Auto Publish field callback
     */
    public function auto_publish_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['auto_publish']) ? $settings['auto_publish'] : false;
        
        echo '<input type="checkbox" id="auto_publish" name="afbw_settings[auto_publish]" value="1"' . checked($value, true, false) . ' />';
        echo '<label for="auto_publish">' . __('Automatically publish generated posts', 'ai-fitness-blog-writer') . '</label>';
        echo '<p class="description">' . __('If enabled, generated posts will be published immediately. Otherwise, they will be saved as drafts.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Auto Set Featured Image field callback
     */
    public function auto_set_featured_image_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['auto_set_featured_image']) ? $settings['auto_set_featured_image'] : true;
        
        echo '<input type="checkbox" id="auto_set_featured_image" name="afbw_settings[auto_set_featured_image]" value="1"' . checked($value, true, false) . ' />';
        echo '<label for="auto_set_featured_image">' . __('Automatically set featured image from Pexels', 'ai-fitness-blog-writer') . '</label>';
        echo '<p class="description">' . __('If enabled, a relevant image will be automatically fetched from Pexels and set as the featured image.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Post Status field callback
     */
    public function post_status_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['post_status']) ? $settings['post_status'] : 'draft';
        
        $statuses = array(
            'draft' => __('Draft', 'ai-fitness-blog-writer'),
            'publish' => __('Published', 'ai-fitness-blog-writer'),
            'private' => __('Private', 'ai-fitness-blog-writer')
        );
        
        echo '<select id="post_status" name="afbw_settings[post_status]">';
        foreach ($statuses as $status_key => $status_label) {
            echo '<option value="' . esc_attr($status_key) . '"' . selected($value, $status_key, false) . '>' . esc_html($status_label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Default status for generated posts.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Post Category field callback
     */
    public function post_category_callback() {
        $settings = get_option('afbw_settings', array());
        $value = isset($settings['post_category']) ? $settings['post_category'] : 1;
        
        $categories = get_categories(array('hide_empty' => false));
        
        echo '<select id="post_category" name="afbw_settings[post_category]">';
        foreach ($categories as $category) {
            echo '<option value="' . $category->term_id . '"' . selected($value, $category->term_id, false) . '>' . esc_html($category->name) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Default category for generated posts.', 'ai-fitness-blog-writer') . '</p>';
    }
    
    /**
     * Test API connection via AJAX
     */
    public function test_api_connection() {
        check_ajax_referer('afbw_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }
        
        $api_type = sanitize_text_field($_POST['api_type']);
        $api_key = sanitize_text_field($_POST['api_key']);
        
        if ($api_type === 'openrouter') {
            $result = $this->test_openrouter_connection($api_key);
        } elseif ($api_type === 'pexels') {
            $result = $this->test_pexels_connection($api_key);
        } else {
            $result = array('success' => false, 'message' => __('Invalid API type', 'ai-fitness-blog-writer'));
        }
        
        wp_send_json($result);
    }
    
    /**
     * Test OpenRouter connection
     */
    private function test_openrouter_connection($api_key) {
        $response = wp_remote_get('https://openrouter.ai/api/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code === 200) {
            return array('success' => true, 'message' => __('OpenRouter connection successful!', 'ai-fitness-blog-writer'));
        } else {
            return array('success' => false, 'message' => __('OpenRouter connection failed. Please check your API key.', 'ai-fitness-blog-writer'));
        }
    }
    
    /**
     * Test Pexels connection
     */
    private function test_pexels_connection($api_key) {
        $response = wp_remote_get('https://api.pexels.com/v1/search?query=fitness&per_page=1', array(
            'headers' => array(
                'Authorization' => $api_key
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code === 200) {
            return array('success' => true, 'message' => __('Pexels connection successful!', 'ai-fitness-blog-writer'));
        } else {
            return array('success' => false, 'message' => __('Pexels connection failed. Please check your API key.', 'ai-fitness-blog-writer'));
        }
    }
}
