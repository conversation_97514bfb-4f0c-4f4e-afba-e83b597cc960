<?php
/**
 * AJAX functionality for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        // AJAX actions for logged-in users
        add_action('wp_ajax_afbw_generate_content', array($this, 'generate_content'));
        add_action('wp_ajax_afbw_publish_post', array($this, 'publish_post'));
        add_action('wp_ajax_afbw_save_draft', array($this, 'save_draft'));
        add_action('wp_ajax_afbw_test_api_connection', array($this, 'test_api_connection'));
        add_action('wp_ajax_afbw_delete_generated_post', array($this, 'delete_generated_post'));
    }
    
    /**
     * Generate content via AJAX
     */
    public function generate_content() {
        // Verify nonce and permissions
        AFBW_Security::verify_ajax_nonce();
        AFBW_Security::verify_user_capability('manage_options');

        // Check rate limit
        $rate_limit_check = AFBW_Security::check_rate_limit();
        if (is_wp_error($rate_limit_check)) {
            wp_send_json_error($rate_limit_check->get_error_message());
        }

        // Get and sanitize input data
        $topic = AFBW_Security::sanitize_topic($_POST['topic']);
        $target_audience = AFBW_Security::sanitize_audience($_POST['target_audience']);
        $keywords = AFBW_Security::sanitize_keywords($_POST['keywords']);
        $word_count = AFBW_Security::validate_word_count($_POST['word_count']);

        // Validate required fields
        if (empty($topic)) {
            wp_send_json_error(__('Topic is required', 'ai-fitness-blog-writer'));
        }

        // Log the generation attempt
        AFBW_Security::log_security_event('content_generation_attempt', array(
            'topic' => $topic,
            'word_count' => $word_count
        ));
        
        // Initialize generator
        require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';
        $generator = new AFBW_Generator();
        
        // Generate content
        $content_result = $generator->generate_content($topic, $target_audience, $keywords, $word_count);
        
        if (is_wp_error($content_result)) {
            wp_send_json_error($content_result->get_error_message());
        }
        
        // Fetch featured image
        $settings = get_option('afbw_settings', array());
        $featured_image = null;
        $featured_image_url = '';
        
        if (!empty($settings['pexels_api_key']) && ($settings['auto_set_featured_image'] ?? true)) {
            $image_result = $generator->fetch_featured_image($topic, $keywords);
            
            if (!is_wp_error($image_result)) {
                $featured_image = $image_result;
                $featured_image_url = $image_result['url'];
            }
        }
        
        // Save to database
        $save_data = array(
            'topic' => $topic,
            'target_audience' => $target_audience,
            'keywords' => $keywords,
            'word_count' => $word_count,
            'content' => $content_result['content'],
            'featured_image_url' => $featured_image_url
        );
        
        $saved_id = $generator->save_generated_content($save_data);
        
        if (is_wp_error($saved_id)) {
            wp_send_json_error($saved_id->get_error_message());
        }
        
        // Prepare response
        $response = array(
            'title' => $content_result['title'],
            'content' => $content_result['content'],
            'featured_image' => $featured_image,
            'generated_id' => $saved_id,
            'word_count_actual' => str_word_count(strip_tags($content_result['content']))
        );
        
        wp_send_json_success($response);
    }
    
    /**
     * Publish post via AJAX
     */
    public function publish_post() {
        // Verify nonce
        check_ajax_referer('afbw_nonce', 'nonce');
        
        // Check user permissions
        if (!current_user_can('publish_posts')) {
            wp_send_json_error(__('Insufficient permissions to publish posts', 'ai-fitness-blog-writer'));
        }
        
        // Get input data
        $title = sanitize_text_field($_POST['title']);
        $content = wp_kses_post($_POST['content']);
        $generated_id = absint($_POST['generated_id']);
        $featured_image_data = isset($_POST['featured_image']) ? $_POST['featured_image'] : null;
        
        // Validate required fields
        if (empty($title) || empty($content)) {
            wp_send_json_error(__('Title and content are required', 'ai-fitness-blog-writer'));
        }
        
        // Get settings
        $settings = get_option('afbw_settings', array());
        $post_category = $settings['post_category'] ?? 1;
        $post_status = $settings['post_status'] ?? 'draft';
        
        // Initialize generator
        require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';
        $generator = new AFBW_Generator();
        
        // Create the post
        $post_id = $generator->create_post($title, $content, $post_category, $post_status);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error($post_id->get_error_message());
        }
        
        // Set featured image if available
        if ($featured_image_data && !empty($featured_image_data['url'])) {
            $image_result = $generator->set_featured_image($post_id, $featured_image_data);
            
            if (is_wp_error($image_result)) {
                // Log the error but don't fail the entire operation
                error_log('AFBW: Failed to set featured image: ' . $image_result->get_error_message());
            }
        }
        
        // Update the generated content record
        if ($generated_id) {
            global $wpdb;
            $wpdb->update(
                $wpdb->prefix . 'afbw_generated_posts',
                array(
                    'post_id' => $post_id,
                    'status' => 'published'
                ),
                array('id' => $generated_id),
                array('%d', '%s'),
                array('%d')
            );
        }
        
        // Prepare response
        $response = array(
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'view_url' => get_permalink($post_id),
            'status' => $post_status
        );
        
        wp_send_json_success($response);
    }
    
    /**
     * Save as draft via AJAX
     */
    public function save_draft() {
        // Verify nonce
        check_ajax_referer('afbw_nonce', 'nonce');
        
        // Check user permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions to create posts', 'ai-fitness-blog-writer'));
        }
        
        // Get input data
        $title = sanitize_text_field($_POST['title']);
        $content = wp_kses_post($_POST['content']);
        $generated_id = absint($_POST['generated_id']);
        $featured_image_data = isset($_POST['featured_image']) ? $_POST['featured_image'] : null;
        
        // Validate required fields
        if (empty($title) || empty($content)) {
            wp_send_json_error(__('Title and content are required', 'ai-fitness-blog-writer'));
        }
        
        // Get settings
        $settings = get_option('afbw_settings', array());
        $post_category = $settings['post_category'] ?? 1;
        
        // Initialize generator
        require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';
        $generator = new AFBW_Generator();
        
        // Create the post as draft
        $post_id = $generator->create_post($title, $content, $post_category, 'draft');
        
        if (is_wp_error($post_id)) {
            wp_send_json_error($post_id->get_error_message());
        }
        
        // Set featured image if available
        if ($featured_image_data && !empty($featured_image_data['url'])) {
            $image_result = $generator->set_featured_image($post_id, $featured_image_data);
            
            if (is_wp_error($image_result)) {
                // Log the error but don't fail the entire operation
                error_log('AFBW: Failed to set featured image: ' . $image_result->get_error_message());
            }
        }
        
        // Update the generated content record
        if ($generated_id) {
            global $wpdb;
            $wpdb->update(
                $wpdb->prefix . 'afbw_generated_posts',
                array(
                    'post_id' => $post_id,
                    'status' => 'draft'
                ),
                array('id' => $generated_id),
                array('%d', '%s'),
                array('%d')
            );
        }
        
        // Prepare response
        $response = array(
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'status' => 'draft'
        );
        
        wp_send_json_success($response);
    }
    
    /**
     * Test API connection via AJAX
     */
    public function test_api_connection() {
        // Verify nonce
        check_ajax_referer('afbw_nonce', 'nonce');
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }
        
        $api_type = sanitize_text_field($_POST['api_type']);
        $api_key = sanitize_text_field($_POST['api_key']);
        
        if ($api_type === 'openrouter') {
            $result = $this->test_openrouter_connection($api_key);
        } elseif ($api_type === 'pexels') {
            $result = $this->test_pexels_connection($api_key);
        } else {
            wp_send_json_error(__('Invalid API type', 'ai-fitness-blog-writer'));
        }
        
        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * Delete generated post via AJAX
     */
    public function delete_generated_post() {
        // Verify nonce
        check_ajax_referer('afbw_nonce', 'nonce');
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }
        
        $generated_id = absint($_POST['generated_id']);
        
        if (!$generated_id) {
            wp_send_json_error(__('Invalid generated post ID', 'ai-fitness-blog-writer'));
        }
        
        global $wpdb;
        
        // Get the post data first
        $generated_post = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}afbw_generated_posts WHERE id = %d",
                $generated_id
            )
        );
        
        if (!$generated_post) {
            wp_send_json_error(__('Generated post not found', 'ai-fitness-blog-writer'));
        }
        
        // Delete the WordPress post if it exists
        if ($generated_post->post_id) {
            wp_delete_post($generated_post->post_id, true);
        }
        
        // Delete the generated post record
        $result = $wpdb->delete(
            $wpdb->prefix . 'afbw_generated_posts',
            array('id' => $generated_id),
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(__('Failed to delete generated post', 'ai-fitness-blog-writer'));
        }
        
        wp_send_json_success(__('Generated post deleted successfully', 'ai-fitness-blog-writer'));
    }
    
    /**
     * Test OpenRouter connection
     */
    private function test_openrouter_connection($api_key) {
        $response = wp_remote_get('https://openrouter.ai/api/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code === 200) {
            return array('success' => true, 'message' => __('OpenRouter connection successful!', 'ai-fitness-blog-writer'));
        } else {
            return array('success' => false, 'message' => __('OpenRouter connection failed. Please check your API key.', 'ai-fitness-blog-writer'));
        }
    }
    
    /**
     * Test Pexels connection
     */
    private function test_pexels_connection($api_key) {
        $response = wp_remote_get('https://api.pexels.com/v1/search?query=fitness&per_page=1', array(
            'headers' => array(
                'Authorization' => $api_key
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code === 200) {
            return array('success' => true, 'message' => __('Pexels connection successful!', 'ai-fitness-blog-writer'));
        } else {
            return array('success' => false, 'message' => __('Pexels connection failed. Please check your API key.', 'ai-fitness-blog-writer'));
        }
    }
}
