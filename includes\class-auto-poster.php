<?php
/**
 * Auto-posting functionality for AI Fitness Blog Writer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AFBW_Auto_Poster {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Hook into WordPress cron
        add_action('afbw_auto_posting_check', array($this, 'check_and_generate_posts'));
        add_action('afbw_daily_cleanup', array($this, 'daily_cleanup'));
        
        // Add admin hooks
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Initialize admin functionality
     */
    public function admin_init() {
        // Add AJAX handlers for auto-posting management
        add_action('wp_ajax_afbw_toggle_auto_posting', array($this, 'toggle_auto_posting'));
        add_action('wp_ajax_afbw_get_auto_posting_status', array($this, 'get_auto_posting_status'));
        add_action('wp_ajax_afbw_force_generate_post', array($this, 'force_generate_post'));
    }
    
    /**
     * Main function to check and generate posts
     */
    public function check_and_generate_posts() {
        $settings = get_option('afbw_settings', array());
        
        // Check if auto-posting is enabled
        if (empty($settings['auto_posting_enabled'])) {
            return;
        }
        
        // Check if we're within posting hours
        if (!$this->is_within_posting_hours()) {
            return;
        }
        
        // Check if we need to generate a post
        if ($this->should_generate_post()) {
            $this->generate_auto_post();
        }
    }
    
    /**
     * Check if current time is within posting hours
     */
    private function is_within_posting_hours() {
        $settings = get_option('afbw_settings', array());
        $start_time = $settings['auto_posting_start_time'] ?? '08:00';
        $end_time = $settings['auto_posting_end_time'] ?? '18:00';
        
        $current_time = current_time('H:i');
        
        return ($current_time >= $start_time && $current_time <= $end_time);
    }
    
    /**
     * Check if we should generate a post now
     */
    private function should_generate_post() {
        $settings = get_option('afbw_settings', array());
        $frequency = $settings['auto_posting_frequency'] ?? 4;
        $min_interval = $settings['auto_posting_min_interval'] ?? 2;
        
        // Get today's post count
        $today_count = $this->get_today_auto_post_count();
        
        // Check if we've reached daily limit
        if ($today_count >= $frequency) {
            return false;
        }
        
        // Check minimum interval since last post
        $last_post_time = get_option('afbw_last_auto_post_time', 0);
        $min_interval_seconds = $min_interval * 3600; // Convert hours to seconds
        
        if ((time() - $last_post_time) < $min_interval_seconds) {
            return false;
        }
        
        // If randomization is enabled, add some randomness
        if ($settings['auto_posting_randomize'] ?? true) {
            $random_chance = rand(1, 100);
            // 30% chance per check (since we check hourly, this gives good distribution)
            return $random_chance <= 30;
        }
        
        return true;
    }
    
    /**
     * Get count of auto-generated posts today
     */
    private function get_today_auto_post_count() {
        global $wpdb;
        
        $today_start = date('Y-m-d 00:00:00');
        $today_end = date('Y-m-d 23:59:59');
        
        $count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}afbw_generated_posts 
                WHERE status = 'auto_published' 
                AND created_at BETWEEN %s AND %s",
                $today_start,
                $today_end
            )
        );
        
        return intval($count);
    }
    
    /**
     * Generate an automatic post
     */
    public function generate_auto_post() {
        try {
            // Check API rate limits
            if (!$this->check_api_rate_limits()) {
                $this->log_error('API rate limits exceeded, skipping post generation');
                return false;
            }

            // Check daily quota
            if (!$this->check_daily_quota()) {
                $this->log_error('Daily quota exceeded, skipping post generation');
                return false;
            }

            // Get a random topic
            $topic_data = $this->get_random_topic();
            if (!$topic_data) {
                $this->log_error('No topics available for auto-posting');
                return false;
            }
            
            // Get settings
            $settings = get_option('afbw_settings', array());
            
            // Randomize word count within range
            $min_words = $settings['auto_posting_word_count_min'] ?? 600;
            $max_words = $settings['auto_posting_word_count_max'] ?? 1200;
            $word_count = rand($min_words, $max_words);
            
            // Initialize generator
            require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';
            $generator = new AFBW_Generator();
            
            // Generate content with retry logic
            $content_result = $generator->generate_content(
                $topic_data['topic'],
                $topic_data['audience'],
                $topic_data['keywords'],
                $word_count
            );

            if (is_wp_error($content_result)) {
                return $this->handle_generation_error($content_result->get_error_message(), $topic_data);
            }

            // Complete post creation
            return $this->complete_post_creation($content_result, $topic_data, $word_count);
            
        } catch (Exception $e) {
            $this->log_error('Auto-posting exception: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a random topic for auto-posting
     */
    private function get_random_topic() {
        // Load topic pool class
        require_once AFBW_PLUGIN_PATH . 'includes/class-topic-pool.php';

        $settings = get_option('afbw_settings', array());
        $enabled_categories = $settings['auto_posting_categories'] ?? array('general');

        // If specific categories are set, use them
        if (!empty($enabled_categories) && !in_array('general', $enabled_categories)) {
            $topics = AFBW_Topic_Pool::get_topics_by_categories($enabled_categories);
            if (!empty($topics)) {
                $random_key = array_rand($topics);
                return $topics[$random_key];
            }
        }

        // Otherwise, use weighted random selection for variety
        return AFBW_Topic_Pool::get_weighted_random_topic();
    }
    
    /**
     * Set featured image for post
     */
    private function set_featured_image($post_id, $image_url) {
        // This is a simplified version - in production you'd want to download and attach the image
        // For now, we'll just store the URL as post meta
        update_post_meta($post_id, '_afbw_featured_image_url', $image_url);
    }
    
    /**
     * Daily cleanup tasks
     */
    public function daily_cleanup() {
        // Clean up old generated posts
        AFBW_Security::cleanup_old_posts();
        
        // Reset daily counters if needed
        $this->reset_daily_counters();
    }
    
    /**
     * Reset daily counters
     */
    private function reset_daily_counters() {
        // This can be used for any daily reset operations
        delete_transient('afbw_daily_post_count');
    }
    
    /**
     * Log success message
     */
    private function log_success($message) {
        error_log('[AFBW Auto-Poster SUCCESS] ' . $message);
        
        // Store in database log
        $this->store_log('success', $message);
    }
    
    /**
     * Log error message
     */
    private function log_error($message) {
        error_log('[AFBW Auto-Poster ERROR] ' . $message);
        
        // Store in database log
        $this->store_log('error', $message);
    }
    

    
    /**
     * AJAX: Toggle auto-posting
     */
    public function toggle_auto_posting() {
        check_ajax_referer('afbw_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }
        
        $enabled = sanitize_text_field($_POST['enabled']) === 'true';
        
        $settings = get_option('afbw_settings', array());
        $settings['auto_posting_enabled'] = $enabled;
        update_option('afbw_settings', $settings);
        
        wp_send_json_success(array(
            'enabled' => $enabled,
            'message' => $enabled ? 
                __('Auto-posting enabled', 'ai-fitness-blog-writer') : 
                __('Auto-posting disabled', 'ai-fitness-blog-writer')
        ));
    }
    
    /**
     * AJAX: Get auto-posting status
     */
    public function get_auto_posting_status() {
        check_ajax_referer('afbw_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }
        
        $settings = get_option('afbw_settings', array());
        $today_count = $this->get_today_auto_post_count();
        $last_post_time = get_option('afbw_last_auto_post_time', 0);
        
        wp_send_json_success(array(
            'enabled' => $settings['auto_posting_enabled'] ?? false,
            'today_count' => $today_count,
            'daily_limit' => $settings['auto_posting_frequency'] ?? 4,
            'last_post_time' => $last_post_time,
            'next_check' => wp_next_scheduled('afbw_auto_posting_check')
        ));
    }
    
    /**
     * AJAX: Force generate a post now
     */
    public function force_generate_post() {
        check_ajax_referer('afbw_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-fitness-blog-writer'));
        }

        $post_id = $this->generate_auto_post();

        if ($post_id) {
            wp_send_json_success(array(
                'post_id' => $post_id,
                'message' => __('Post generated successfully', 'ai-fitness-blog-writer')
            ));
        } else {
            wp_send_json_error(__('Failed to generate post', 'ai-fitness-blog-writer'));
        }
    }

    /**
     * Reschedule cron jobs (called when settings change)
     */
    public static function reschedule_cron_jobs() {
        // Clear existing schedules
        wp_clear_scheduled_hook('afbw_auto_posting_check');
        wp_clear_scheduled_hook('afbw_daily_cleanup');

        // Reschedule
        wp_schedule_event(time(), 'hourly', 'afbw_auto_posting_check');
        wp_schedule_event(time(), 'daily', 'afbw_daily_cleanup');
    }

    /**
     * Get cron status information
     */
    public static function get_cron_status() {
        return array(
            'auto_posting_next' => wp_next_scheduled('afbw_auto_posting_check'),
            'daily_cleanup_next' => wp_next_scheduled('afbw_daily_cleanup'),
            'wp_cron_disabled' => defined('DISABLE_WP_CRON') && DISABLE_WP_CRON,
            'current_time' => time()
        );
    }

    /**
     * Check API rate limits
     */
    private function check_api_rate_limits() {
        $rate_limit_key = 'afbw_api_rate_limit_' . date('Y-m-d-H');
        $current_count = get_transient($rate_limit_key) ?: 0;

        // Limit to 10 API calls per hour to be safe
        $hourly_limit = apply_filters('afbw_hourly_api_limit', 10);

        if ($current_count >= $hourly_limit) {
            return false;
        }

        // Increment counter
        set_transient($rate_limit_key, $current_count + 1, HOUR_IN_SECONDS);

        return true;
    }

    /**
     * Check daily quota
     */
    private function check_daily_quota() {
        $settings = get_option('afbw_settings', array());
        $daily_limit = $settings['auto_posting_frequency'] ?? 4;

        // Add a safety buffer - never exceed 150% of set limit
        $safety_limit = ceil($daily_limit * 1.5);

        $today_count = $this->get_today_auto_post_count();

        return $today_count < $safety_limit;
    }

    /**
     * Enhanced error handling with retry logic
     */
    private function handle_generation_error($error_message, $topic_data, $attempt = 1) {
        $max_attempts = 3;

        $this->log_error("Generation attempt {$attempt} failed: {$error_message}");

        if ($attempt < $max_attempts) {
            // Wait a bit before retry
            sleep(5);

            // Try with a different topic if available
            $new_topic = $this->get_random_topic();
            if ($new_topic && $new_topic['topic'] !== $topic_data['topic']) {
                $this->log_error("Retrying with different topic: {$new_topic['topic']}");
                return $this->retry_generation($new_topic, $attempt + 1);
            }
        }

        return false;
    }

    /**
     * Retry generation with different parameters
     */
    private function retry_generation($topic_data, $attempt) {
        try {
            $settings = get_option('afbw_settings', array());

            // Use more conservative word count for retries
            $min_words = max(400, ($settings['auto_posting_word_count_min'] ?? 600) - 200);
            $max_words = min(1000, ($settings['auto_posting_word_count_max'] ?? 1200) - 200);
            $word_count = rand($min_words, $max_words);

            require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';
            $generator = new AFBW_Generator();

            $content_result = $generator->generate_content(
                $topic_data['topic'],
                $topic_data['audience'],
                $topic_data['keywords'],
                $word_count
            );

            if (is_wp_error($content_result)) {
                return $this->handle_generation_error($content_result->get_error_message(), $topic_data, $attempt);
            }

            // Continue with post creation...
            return $this->complete_post_creation($content_result, $topic_data, $word_count);

        } catch (Exception $e) {
            return $this->handle_generation_error($e->getMessage(), $topic_data, $attempt);
        }
    }

    /**
     * Complete post creation after successful content generation
     */
    private function complete_post_creation($content_result, $topic_data, $word_count) {
        $settings = get_option('afbw_settings', array());

        // Get featured image with error handling
        $featured_image_url = '';
        if (!empty($settings['pexels_api_key']) && ($settings['auto_set_featured_image'] ?? true)) {
            try {
                require_once AFBW_PLUGIN_PATH . 'includes/class-generator.php';
                $generator = new AFBW_Generator();
                $image_result = $generator->fetch_featured_image($topic_data['topic'], $topic_data['keywords']);

                if (!is_wp_error($image_result)) {
                    $featured_image_url = $image_result['url'];
                }
            } catch (Exception $e) {
                $this->log_error('Featured image fetch failed: ' . $e->getMessage());
                // Continue without featured image
            }
        }

        // Save to database
        $generated_id = $generator->save_generated_content(array(
            'topic' => $topic_data['topic'],
            'target_audience' => $topic_data['audience'],
            'keywords' => $topic_data['keywords'],
            'word_count' => $word_count,
            'content' => $content_result['content'],
            'featured_image_url' => $featured_image_url
        ));

        if (is_wp_error($generated_id)) {
            $this->log_error('Failed to save generated content: ' . $generated_id->get_error_message());
            return false;
        }

        // Create WordPress post
        $post_category = $settings['post_category'] ?? 1;
        $post_id = $generator->create_post(
            $content_result['title'],
            $content_result['content'],
            $post_category,
            'publish'
        );

        if (is_wp_error($post_id)) {
            $this->log_error('Failed to create WordPress post: ' . $post_id->get_error_message());
            return false;
        }

        // Set featured image if available
        if ($featured_image_url) {
            $this->set_featured_image($post_id, $featured_image_url);
        }

        // Update database record
        global $wpdb;
        $wpdb->update(
            $wpdb->prefix . 'afbw_generated_posts',
            array(
                'post_id' => $post_id,
                'status' => 'auto_published'
            ),
            array('id' => $generated_id),
            array('%d', '%s'),
            array('%d')
        );

        // Update last post time
        update_option('afbw_last_auto_post_time', time());

        // Log success with details
        $this->log_success("Auto-generated post: {$content_result['title']} (ID: {$post_id}, Words: {$word_count})");

        // Update API usage stats
        $this->update_api_usage_stats();

        return $post_id;
    }

    /**
     * Update API usage statistics
     */
    private function update_api_usage_stats() {
        $today = date('Y-m-d');
        $stats = get_option('afbw_api_usage_stats', array());

        if (!isset($stats[$today])) {
            $stats[$today] = array(
                'openrouter_calls' => 0,
                'pexels_calls' => 0,
                'posts_generated' => 0
            );
        }

        $stats[$today]['openrouter_calls']++;
        $stats[$today]['pexels_calls']++;
        $stats[$today]['posts_generated']++;

        // Keep only last 30 days of stats
        $cutoff_date = date('Y-m-d', strtotime('-30 days'));
        foreach ($stats as $date => $data) {
            if ($date < $cutoff_date) {
                unset($stats[$date]);
            }
        }

        update_option('afbw_api_usage_stats', $stats);
    }

    /**
     * Get API usage statistics
     */
    public static function get_api_usage_stats($days = 7) {
        $stats = get_option('afbw_api_usage_stats', array());
        $result = array();

        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $result[$date] = $stats[$date] ?? array(
                'openrouter_calls' => 0,
                'pexels_calls' => 0,
                'posts_generated' => 0
            );
        }

        return array_reverse($result, true);
    }

    /**
     * Enhanced logging with context
     */
    private function log_with_context($type, $message, $context = array()) {
        $full_message = $message;

        if (!empty($context)) {
            $context_str = json_encode($context);
            $full_message .= " | Context: {$context_str}";
        }

        // Log to WordPress error log
        error_log("[AFBW Auto-Poster {$type}] {$full_message}");

        // Store in database with context
        global $wpdb;
        $wpdb->insert(
            $wpdb->prefix . 'afbw_auto_posting_logs',
            array(
                'log_type' => $type,
                'message' => $full_message,
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s')
        );

        // Clean up old logs (keep only last 1000 entries)
        $this->cleanup_old_logs();
    }

    /**
     * Clean up old log entries
     */
    private function cleanup_old_logs() {
        global $wpdb;

        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}afbw_auto_posting_logs");

        if ($count > 1000) {
            $wpdb->query(
                "DELETE FROM {$wpdb->prefix}afbw_auto_posting_logs
                WHERE id NOT IN (
                    SELECT id FROM (
                        SELECT id FROM {$wpdb->prefix}afbw_auto_posting_logs
                        ORDER BY created_at DESC
                        LIMIT 1000
                    ) AS recent_logs
                )"
            );
        }
    }

    /**
     * Enhanced success logging
     */
    private function log_success($message, $context = array()) {
        $this->log_with_context('success', $message, $context);
    }

    /**
     * Enhanced error logging
     */
    private function log_error($message, $context = array()) {
        $this->log_with_context('error', $message, $context);
    }
}
