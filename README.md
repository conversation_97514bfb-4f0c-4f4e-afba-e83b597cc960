# AI Fitness Blog Writer

A powerful WordPress plugin that generates engaging fitness blog posts using AI and automatically sets featured images from Pexels.

## 🚀 Features

- **AI-Powered Content Generation**: Uses OpenRouter.ai to access DeepSeek R1 (free) and other AI models
- **Automatic Featured Images**: Fetches relevant images from Pexels API
- **Customizable Content**: Specify topic, target audience, keywords, and word count
- **WordPress Integration**: Seamlessly integrates with WordPress admin panel
- **Content Preview & Editing**: Preview generated content before publishing
- **Multiple Publishing Options**: Save as draft or publish immediately
- **Security First**: Built with WordPress security best practices
- **Responsive Design**: Mobile-friendly admin interface

## 📋 Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- OpenRouter.ai API key (free registration)
- Pexels API key (free registration)

## 🔧 Installation

### Method 1: Manual Installation

1. Download the plugin files
2. Upload the `ai-fitness-blog-writer` folder to `/wp-content/plugins/`
3. Activate the plugin through the 'Plugins' menu in WordPress
4. Configure your API keys in the settings

### Method 2: WordPress Admin

1. Go to Plugins > Add New
2. Upload the plugin zip file
3. Activate the plugin
4. Configure your API keys in the settings

## ⚙️ Configuration

### 1. Get Your API Keys

#### OpenRouter.ai API Key
1. Visit [OpenRouter.ai](https://openrouter.ai/keys)
2. Sign up for a free account
3. Generate an API key
4. Copy the key for plugin configuration

#### Pexels API Key
1. Visit [Pexels API](https://www.pexels.com/api/)
2. Sign up for a free account
3. Generate an API key
4. Copy the key for plugin configuration

### 2. Configure Plugin Settings

1. Go to **AI Blog Writer > Settings** in your WordPress admin
2. Enter your OpenRouter API key
3. Select your preferred AI model (DeepSeek models are free)
4. Enter your Pexels API key
5. Configure default settings for content generation
6. Save your settings

## 🎯 Usage

### Generating Your First Blog Post

1. Navigate to **AI Blog Writer > Generate Post**
2. Fill in the form:
   - **Blog Topic**: Enter your main topic (e.g., "Intermittent fasting for weight loss")
   - **Target Audience**: Specify your audience (e.g., "Beginners", "Advanced athletes")
   - **Keywords**: Add comma-separated keywords for SEO
   - **Word Count**: Select desired length (300-2000 words)
3. Click **Generate Article**
4. Wait for the AI to generate your content
5. Preview the generated content
6. Edit if needed or publish directly

### Content Management

- **Preview**: Review generated content before publishing
- **Edit**: Modify content using WordPress editor
- **Publish**: Publish immediately or save as draft
- **Manage**: View all generated posts in the Generated Posts section

## 🔒 Security Features

- **Nonce Verification**: All AJAX requests are protected with WordPress nonces
- **User Capability Checks**: Only authorized users can generate content
- **Input Sanitization**: All user inputs are properly sanitized
- **Rate Limiting**: Prevents abuse with request rate limiting
- **Content Validation**: Generated content is validated for safety
- **Secure API Handling**: API keys are stored securely

## 🎨 Customization

### Available AI Models

- **DeepSeek Chat** (Free)
- **DeepSeek R1** (Free)
- **GPT-3.5 Turbo**
- **GPT-4**
- **Claude 3 Haiku**
- **Llama 3.1 8B**

### Content Settings

- **Default Word Count**: Set your preferred article length
- **Auto Publish**: Automatically publish generated posts
- **Auto Featured Image**: Automatically set images from Pexels
- **Default Category**: Set default category for new posts
- **Post Status**: Choose default status (draft/published/private)

## 🛠️ Troubleshooting

### Common Issues

#### "API key not configured" Error
- Ensure you've entered valid API keys in the settings
- Test your API connections using the "Test Connection" buttons

#### "Rate limit exceeded" Error
- Wait an hour before generating more content
- The plugin limits requests to prevent abuse

#### Generated content is too short/long
- Adjust the word count setting
- Check your AI model selection
- Ensure your topic is specific enough

#### Featured image not loading
- Verify your Pexels API key is correct
- Check if the topic has relevant images available
- Try using more specific keywords

### Debug Mode

Enable WordPress debug mode to see detailed error logs:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 📊 Performance

- **Generation Time**: Typically 30-60 seconds per article
- **Rate Limits**: 10 requests per hour per user
- **Content Quality**: High-quality, SEO-optimized content
- **Image Quality**: High-resolution images from Pexels

## 🔄 Updates

The plugin includes automatic update notifications. Always backup your site before updating.

## 📝 Changelog

### Version 1.0.0
- Initial release
- AI content generation with OpenRouter.ai
- Pexels image integration
- WordPress admin interface
- Security features
- Content management system

## 🤝 Support

For support and feature requests:

1. Check the troubleshooting section above
2. Review WordPress error logs
3. Contact plugin support with detailed error information

## 📄 License

This plugin is licensed under the GPL v2 or later.

## 🙏 Credits

- **OpenRouter.ai** for AI model access
- **Pexels** for high-quality stock photos
- **WordPress** for the amazing platform
- **DeepSeek** for free AI models

## 🔮 Roadmap

Future features planned:
- Bulk content generation
- Content templates
- SEO optimization suggestions
- Social media integration
- Analytics dashboard
- Custom AI model training

---

**Made with ❤️ for the WordPress community**
