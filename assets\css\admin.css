/**
 * Admin styles for AI Fitness Blog Writer
 */

/* Main admin wrapper */
.afbw-admin-wrap {
    max-width: 1200px;
    margin: 20px 0;
}

.afbw-admin-wrap h1 {
    margin-bottom: 10px;
}

.afbw-admin-wrap .description {
    font-size: 14px;
    color: #666;
    margin-bottom: 30px;
}

/* Generator container */
.afbw-generator-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.afbw-form .form-table {
    margin-bottom: 20px;
}

.afbw-form .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.afbw-form .form-table td {
    padding: 15px 10px;
}

.afbw-form .regular-text {
    width: 400px;
}

.afbw-form .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Button styles */
#generate-article-btn {
    position: relative;
    padding: 10px 20px;
    font-size: 14px;
    min-width: 150px;
}

#generate-article-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#afbw-spinner {
    float: none;
    margin-left: 10px;
    visibility: hidden;
}

#afbw-spinner.is-active {
    visibility: visible;
}

/* Preview container */
.afbw-preview-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin-top: 30px;
}

.afbw-preview-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #495057;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}

.afbw-preview-content {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    max-height: 500px;
    overflow-y: auto;
    line-height: 1.6;
}

.afbw-preview-content h1,
.afbw-preview-content h2,
.afbw-preview-content h3,
.afbw-preview-content h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #333;
}

.afbw-preview-content h1:first-child,
.afbw-preview-content h2:first-child,
.afbw-preview-content h3:first-child,
.afbw-preview-content h4:first-child {
    margin-top: 0;
}

.afbw-preview-content p {
    margin-bottom: 15px;
}

.afbw-preview-content ul,
.afbw-preview-content ol {
    margin-bottom: 15px;
    padding-left: 20px;
}

.afbw-preview-content li {
    margin-bottom: 5px;
}

.afbw-preview-content blockquote {
    border-left: 4px solid #007cba;
    padding-left: 15px;
    margin: 20px 0;
    font-style: italic;
    color: #666;
}

.afbw-preview-content code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.afbw-preview-content pre {
    background: #f1f1f1;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 15px 0;
}

/* Preview actions */
.afbw-preview-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.afbw-preview-actions .button {
    padding: 8px 16px;
}

/* Edit container */
.afbw-edit-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 30px;
}

.afbw-edit-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #495057;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}

.afbw-edit-container .form-table th {
    width: 150px;
}

.afbw-edit-container .large-text {
    width: 100%;
}

/* Featured image preview */
.afbw-featured-image-preview {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.afbw-featured-image-preview img {
    max-width: 300px;
    height: auto;
    border-radius: 4px;
    margin-bottom: 10px;
}

.afbw-featured-image-info {
    font-size: 12px;
    color: #666;
}

.afbw-featured-image-info a {
    color: #007cba;
    text-decoration: none;
}

.afbw-featured-image-info a:hover {
    text-decoration: underline;
}

/* Progress indicator */
.afbw-progress {
    background: #f1f1f1;
    border-radius: 10px;
    padding: 3px;
    margin: 15px 0;
}

.afbw-progress-bar {
    background: linear-gradient(90deg, #007cba, #005a87);
    height: 20px;
    border-radius: 8px;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
}

/* Status messages */
.afbw-status-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin: 15px 0;
    font-weight: 500;
}

.afbw-status-message.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.afbw-status-message.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.afbw-status-message.info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.afbw-status-message.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Loading states */
.afbw-loading {
    opacity: 0.6;
    pointer-events: none;
}

.afbw-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

/* Word count display */
.afbw-word-count {
    font-size: 12px;
    color: #666;
    margin-top: 10px;
    text-align: right;
}

/* Responsive design */
@media (max-width: 768px) {
    .afbw-admin-wrap {
        margin: 10px;
    }
    
    .afbw-form .regular-text {
        width: 100%;
        max-width: 400px;
    }
    
    .afbw-form .form-table th,
    .afbw-form .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .afbw-form .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .afbw-preview-actions {
        flex-direction: column;
    }
    
    .afbw-preview-actions .button {
        width: 100%;
        text-align: center;
    }
    
    .afbw-featured-image-preview img {
        max-width: 100%;
    }
}

/* Animation for smooth transitions */
.afbw-fade-in {
    animation: afbwFadeIn 0.3s ease-in;
}

@keyframes afbwFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.afbw-slide-down {
    animation: afbwSlideDown 0.3s ease-out;
}

@keyframes afbwSlideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 1000px;
        transform: translateY(0);
    }
}

/* Custom scrollbar for preview content */
.afbw-preview-content::-webkit-scrollbar {
    width: 8px;
}

.afbw-preview-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.afbw-preview-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.afbw-preview-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Tooltip styles */
.afbw-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.afbw-tooltip .afbw-tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.afbw-tooltip .afbw-tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.afbw-tooltip:hover .afbw-tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Settings page specific styles */
.afbw-settings-wrap .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
}

.afbw-settings-wrap .form-table td {
    padding: 20px 10px;
}

.afbw-test-result {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    display: none;
    font-weight: 500;
}

.afbw-test-result.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.afbw-test-result.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Plugin branding */
.afbw-brand {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.afbw-brand-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #007cba, #005a87);
    border-radius: 6px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.afbw-brand-text h1 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.afbw-brand-text .description {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Enhanced button styles */
.button.afbw-primary {
    background: linear-gradient(135deg, #007cba, #005a87);
    border-color: #005a87;
    color: white;
    text-shadow: none;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.2);
    transition: all 0.2s ease;
}

.button.afbw-primary:hover {
    background: linear-gradient(135deg, #005a87, #004066);
    border-color: #004066;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 124, 186, 0.3);
}

.button.afbw-secondary {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
    transition: all 0.2s ease;
}

.button.afbw-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

/* Card-style containers */
.afbw-card {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    transition: box-shadow 0.2s ease;
}

.afbw-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.afbw-card-header {
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 16px;
    margin-bottom: 20px;
}

.afbw-card-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.afbw-card-header .description {
    margin: 4px 0 0 0;
    color: #666;
    font-size: 14px;
}

/* Enhanced form styling */
.afbw-form-group {
    margin-bottom: 24px;
}

.afbw-form-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.afbw-form-input {
    width: 100%;
    max-width: 400px;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.afbw-form-input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

.afbw-form-help {
    margin-top: 6px;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* Stats and metrics */
.afbw-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.afbw-stat-card {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.afbw-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #007cba;
    margin-bottom: 8px;
}

.afbw-stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .afbw-card,
    .afbw-generator-container {
        background: #1e1e1e;
        border-color: #333;
        color: #e0e0e0;
    }

    .afbw-preview-content {
        background: #2a2a2a;
        border-color: #444;
        color: #e0e0e0;
    }

    .afbw-form-input {
        background: #2a2a2a;
        border-color: #444;
        color: #e0e0e0;
    }

    .afbw-form-input:focus {
        border-color: #4a9eff;
        box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
    }
}
